(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{4177:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(5155),a=r(2115),n=r(5695),i=r(6874),o=r.n(i),l=r(2177),d=r(8778),c=r(6671),u=r(2108),m=r(1929),g=r(6434),h=r(3294),p=r(742);let v=()=>{var e,t;let r=(0,n.useRouter)(),{login:i}=(0,h.BG)(),[v,f]=(0,a.useState)(!1),[y,x]=(0,a.useState)(!1),{register:b,handleSubmit:w,formState:{errors:j},setValue:S,watch:I}=(0,l.mN)({resolver:(0,d.u)(p.X5),defaultValues:{email:"",password:""}}),N=I(),P=async e=>{try{f(!0);let t=await (0,u.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});(null==t?void 0:t.error)?c.oR.error("Login failed",{description:"Please check your credentials and try again."}):(c.oR.success("Login successful!",{description:"Welcome back to DentCare Pro."}),r.push("/dashboard"))}catch(e){c.oR.error("Login failed",{description:e instanceof Error?e.message:"Please check your credentials and try again."})}finally{f(!1)}},k=async()=>{try{x(!0);let e=await (0,u.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});(null==e?void 0:e.error)?c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."}):(null==e?void 0:e.url)&&(c.oR.success("Login successful!",{description:"Redirecting to dashboard..."}),r.push(e.url))}catch(e){c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."})}finally{x(!1)}};return(0,s.jsx)(m.A,{title:"Welcome back",subtitle:"Sign in to your DentCare Pro account",children:(0,s.jsxs)("form",{onSubmit:w(P),className:"space-y-6",children:[(0,s.jsx)(g.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:N.email,onChange:e=>S("email",e),error:null==(e=j.email)?void 0:e.message,required:!0,autoComplete:"email"}),(0,s.jsx)(g.vy,{label:"Password",type:"password",placeholder:"Enter your password",value:N.password,onChange:e=>S("password",e),error:null==(t=j.password)?void 0:t.message,required:!0,autoComplete:"current-password"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(o(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"Forgot your password?"})}),(0,s.jsx)(g.FX,{isLoading:v,disabled:v||y,loadingText:"Signing in...",children:"Sign in"}),(0,s.jsx)(g.Sq,{}),(0,s.jsx)(g.ID,{provider:"google",isLoading:y,disabled:v||y,onClick:k}),(0,s.jsxs)("div",{className:"p-4 bg-muted/50 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-foreground mb-2",children:"Test Credentials"}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Patient:"})," <EMAIL> / password123"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Dentist:"})," <EMAIL> / password123"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Admin:"})," <EMAIL> / password123"]})]})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)(o(),{href:"/auth/register",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign up"})]})})]})})}},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var s=r(2115);let a=e=>{let t,r=new Set,s=(e,s)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=s?s:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,n={setState:s,getState:a,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(s,a,n);return n},n=e=>e?a(e):a,i=e=>e,o=e=>{let t=n(e),r=e=>(function(e,t=i){let r=s.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return s.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o},5619:(e,t,r)=>{Promise.resolve().then(r.bind(r,4177))},6786:(e,t,r)=>{"use strict";function s(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var s;let a=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),n=null!=(s=r.getItem(e))?s:null;return n instanceof Promise?n.then(a):a(n)},setItem:(e,s)=>r.setItem(e,JSON.stringify(s,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}r.d(t,{KU:()=>s,Zr:()=>n});let a=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>a(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>a(t)(e)}}},n=(e,t)=>(r,n,i)=>{let o,l={storage:s(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},d=!1,c=new Set,u=new Set,m=l.storage;if(!m)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},n,i);let g=()=>{let e=l.partialize({...n()});return m.setItem(l.name,{state:e,version:l.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),g()};let p=e((...e)=>{r(...e),g()},n,i);i.getInitialState=()=>p;let v=()=>{var e,t;if(!m)return;d=!1,c.forEach(e=>{var t;return e(null!=(t=n())?t:p)});let s=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=n())?e:p))||void 0;return a(m.getItem.bind(m))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,a]=e;if(r(o=l.merge(a,null!=(t=n())?t:p),!0),s)return g()}).then(()=>{null==s||s(o,void 0),o=n(),d=!0,u.forEach(e=>e(o))}).catch(e=>{null==s||s(void 0,e)})};return i.persist={setOptions:e=>{l={...l,...e},e.storage&&(m=e.storage)},clearStorage:()=>{null==m||m.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>v(),hasHydrated:()=>d,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},l.skipHydration||v(),o||p}}},e=>{var t=t=>e(e.s=t);e.O(0,[52,919,3,981,698,571,441,684,358],()=>t(5619)),_N_E=e.O()}]);