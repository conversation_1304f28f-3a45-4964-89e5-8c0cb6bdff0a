(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2706:(e,s,t)=>{Promise.resolve().then(t.bind(t,5091))},5091:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(5155),a=t(2115),l=t(5695),n=t(3294),i=t(285),c=t(6695),d=t(6874),o=t.n(d),m=t(1976);let x=()=>{let e=(0,l.useRouter)(),{isAuthenticated:s}=(0,n.As)();return(0,r.jsx)("header",{className:"sticky top-0 z-50 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)(o(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-primary/30",children:(0,r.jsx)(m.A,{className:"w-4 h-4 text-primary"})}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)("span",{className:"text-xl font-bold text-foreground font-poppins",children:"DentCare Pro"})})]}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,r.jsx)(o(),{href:"#features",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Features"}),(0,r.jsx)(o(),{href:"#pricing",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Pricing"}),(0,r.jsx)(o(),{href:"#about",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"About"}),(0,r.jsx)(o(),{href:"#contact",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Contact"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:s?(0,r.jsx)(i.$,{onClick:()=>e.push("/dashboard"),className:"glass-button",children:"Dashboard"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.$,{variant:"ghost",onClick:()=>{e.push("/auth/login")},className:"hidden sm:inline-flex",children:"Sign in"}),(0,r.jsx)(i.$,{onClick:()=>{e.push("/auth/register")},className:"glass-button",children:"Get Started"})]})})]})})})};var u=t(9074),h=t(7434),p=t(5525),f=t(4186),g=t(7580),j=t(2713);function N(){let e=(0,l.useRouter)(),{isAuthenticated:s}=(0,n.As)();(0,a.useEffect)(()=>{s&&e.push("/dashboard")},[s,e]);let t=()=>{e.push("/auth/register")};return(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)(x,{}),(0,r.jsxs)("main",{className:"flex-1",children:[(0,r.jsxs)("section",{className:"relative overflow-hidden bg-gradient-to-br from-background via-background to-secondary/20 px-6 py-24 sm:py-32 lg:px-8",children:[(0,r.jsx)("div",{className:"mx-auto max-w-7xl",children:(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold tracking-tight text-foreground sm:text-6xl font-poppins",children:["Modern Dental Practice",(0,r.jsx)("span",{className:"block text-primary",children:"Management"})]}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-muted-foreground",children:"Streamline your dental practice with our secure, HIPAA-compliant platform. Manage appointments, patient records, and documents with ease."}),(0,r.jsxs)("div",{className:"mt-10 flex items-center justify-center gap-x-6",children:[(0,r.jsx)(i.$,{size:"lg",className:"glass-button",onClick:t,children:"Get Started"}),(0,r.jsx)(i.$,{variant:"outline",size:"lg",onClick:()=>{var e;null==(e=document.getElementById("features"))||e.scrollIntoView({behavior:"smooth"})},children:"Learn More"})]})]})}),(0,r.jsx)("div",{className:"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]",children:(0,r.jsx)("div",{className:"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary to-accent-secondary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"})})]}),(0,r.jsx)("section",{id:"features",className:"py-24 sm:py-32",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins",children:"Everything you need to manage your practice"}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-muted-foreground",children:"Our comprehensive platform provides all the tools you need for efficient dental practice management."})]}),(0,r.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,r.jsxs)("div",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3",children:[(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(u.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Appointment Management"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Streamlined booking system with real-time availability, automated reminders, and easy rescheduling for both patients and staff."})})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Patient Records"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Secure, encrypted patient profiles with medical history, treatment plans, and comprehensive document management."})})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"HIPAA Compliance"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Built-in security features, audit logging, and compliance tools to ensure your practice meets all healthcare regulations."})})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(f.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Real-time Scheduling"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Live calendar updates, instant notifications, and seamless coordination between multiple dentists and treatment rooms."})})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Multi-user Access"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Role-based permissions for dentists, hygienists, and administrative staff with secure access controls and audit trails."})})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)(j.A,{className:"w-6 h-6 text-primary"})}),(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Analytics & Reports"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Comprehensive insights into practice performance, patient trends, and revenue analytics to help grow your business."})})]})]})})]})}),(0,r.jsx)("section",{id:"pricing",className:"py-24 sm:py-32 bg-secondary/20",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins",children:"Choose the perfect plan for your practice"}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-muted-foreground",children:"Flexible pricing options designed to grow with your dental practice."})]}),(0,r.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,r.jsxs)("div",{className:"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3",children:[(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Starter"}),(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Perfect for small practices getting started"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"$49"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"/month"})]})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsxs)("ul",{className:"space-y-3 text-sm text-muted-foreground",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Up to 100 patients"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Basic appointment scheduling"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Patient records management"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Email support"]})]}),(0,r.jsx)(i.$,{className:"w-full mt-6 glass-button",onClick:t,children:"Get Started"})]})]}),(0,r.jsxs)(c.Zp,{className:"glass-card border-primary/50 relative",children:[(0,r.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,r.jsx)("span",{className:"bg-primary text-primary-foreground px-3 py-1 text-xs font-medium rounded-full",children:"Most Popular"})}),(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Professional"}),(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"Ideal for growing dental practices"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"$99"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"/month"})]})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsxs)("ul",{className:"space-y-3 text-sm text-muted-foreground",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Up to 500 patients"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Advanced scheduling & reminders"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Document sharing & storage"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Analytics & reporting"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Priority support"]})]}),(0,r.jsx)(i.$,{className:"w-full mt-6 glass-button",onClick:t,children:"Get Started"})]})]}),(0,r.jsxs)(c.Zp,{className:"glass-card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Enterprise"}),(0,r.jsx)(c.BT,{className:"text-muted-foreground",children:"For large practices and dental groups"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"$199"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"/month"})]})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsxs)("ul",{className:"space-y-3 text-sm text-muted-foreground",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Unlimited patients"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Multi-location support"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Custom integrations"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"Advanced analytics"]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),"24/7 dedicated support"]})]}),(0,r.jsx)(i.$,{className:"w-full mt-6 glass-button",onClick:t,children:"Contact Sales"})]})]})]})})]})}),(0,r.jsx)("section",{id:"about",className:"py-24 sm:py-32",children:(0,r.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,r.jsx)("div",{className:"mx-auto max-w-2xl lg:max-w-none",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-2 lg:items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins",children:"Revolutionizing dental practice management"}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-muted-foreground",children:"DentCare Pro was built by healthcare professionals who understand the unique challenges of running a modern dental practice. Our platform combines cutting-edge technology with intuitive design to streamline your workflow and enhance patient care."}),(0,r.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4",children:(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground",children:"HIPAA Compliant"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Built from the ground up with healthcare security standards in mind."})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4",children:(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground",children:"Easy to Use"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Intuitive interface designed for busy healthcare professionals."})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4",children:(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground",children:"24/7 Support"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Our dedicated support team is here to help whenever you need it."})]})]})]})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"glass-card p-8",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary",children:"10,000+"}),(0,r.jsx)("div",{className:"text-muted-foreground",children:"Appointments Managed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary",children:"500+"}),(0,r.jsx)("div",{className:"text-muted-foreground",children:"Dental Practices"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl font-bold text-primary",children:"99.9%"}),(0,r.jsx)("div",{className:"text-muted-foreground",children:"Uptime Guarantee"})]})]})})})]})})})}),(0,r.jsx)("section",{id:"contact",className:"py-24 sm:py-32 bg-secondary/20",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mx-auto max-w-2xl text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins",children:"Get in touch"}),(0,r.jsx)("p",{className:"mt-6 text-lg leading-8 text-muted-foreground",children:"Ready to transform your dental practice? Contact us today to learn more about DentCare Pro."})]}),(0,r.jsx)("div",{className:"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-3",children:[(0,r.jsxs)(c.Zp,{className:"glass-card text-center",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Sales Inquiries"})}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Interested in DentCare Pro for your practice?"}),(0,r.jsx)("p",{className:"font-medium text-foreground",children:"<EMAIL>"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"(555) 123-4567"}),(0,r.jsx)(i.$,{className:"mt-4 glass-button",onClick:t,children:"Schedule Demo"})]})]}),(0,r.jsxs)(c.Zp,{className:"glass-card text-center",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"Technical Support"})}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Need help with your account or have technical questions?"}),(0,r.jsx)("p",{className:"font-medium text-foreground",children:"<EMAIL>"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"(555) 123-4568"}),(0,r.jsx)(i.$,{variant:"outline",className:"mt-4",children:"Contact Support"})]})]}),(0,r.jsxs)(c.Zp,{className:"glass-card text-center",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{className:"text-xl font-semibold text-foreground",children:"General Information"})}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Questions about our company or partnership opportunities?"}),(0,r.jsx)("p",{className:"font-medium text-foreground",children:"<EMAIL>"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"(555) 123-4569"}),(0,r.jsx)(i.$,{variant:"outline",className:"mt-4",children:"Get in Touch"})]})]})]})})]})})]}),(0,r.jsx)("footer",{className:"border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"mx-auto max-w-7xl px-6 py-12 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-primary/30",children:(0,r.jsx)("span",{className:"text-primary font-bold",children:"D"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-foreground font-poppins",children:"DentCare Pro"})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Modern dental practice management platform designed for healthcare professionals."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-foreground mb-4",children:"Product"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#features",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Features"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#pricing",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Pricing"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/auth/register",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Sign Up"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/auth/login",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Sign In"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-foreground mb-4",children:"Support"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#contact",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Contact Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/support",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Help Center"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/docs",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Documentation"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/status",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"System Status"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-foreground mb-4",children:"Legal"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/privacy",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Privacy Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/terms",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Terms of Service"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/security",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"Security"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"/compliance",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"HIPAA Compliance"})})]})]})]}),(0,r.jsx)("div",{className:"mt-8 pt-8 border-t border-border/40",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 DentCare Pro. All rights reserved. Built with security and compliance in mind."}),(0,r.jsxs)("div",{className:"flex space-x-6 mt-4 sm:mt-0",children:[(0,r.jsxs)("a",{href:"#",className:"text-muted-foreground hover:text-foreground transition-colors",children:[(0,r.jsx)("span",{className:"sr-only",children:"Twitter"}),(0,r.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"})})]}),(0,r.jsxs)("a",{href:"#",className:"text-muted-foreground hover:text-foreground transition-colors",children:[(0,r.jsx)("span",{className:"sr-only",children:"LinkedIn"}),(0,r.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z",clipRule:"evenodd"})})]})]})]})})]})})]})}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n});var r=t(5155);t(2115);var a=t(2911);function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[52,919,240,698,441,684,358],()=>s(2706)),_N_E=e.O()}]);