"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{285:(e,r,t)=>{t.d(r,{$:()=>l});var n=t(5155);t(2115);var a=t(9708),s=t(2085),o=t(2911);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:s,asChild:l=!1,...c}=e,u=l?a.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:s,className:r})),...c})}},2911:(e,r,t)=>{t.d(r,{cn:()=>o,DR:()=>i});var n=t(2596),a=t(9688);t(9509).env.NEXT_PUBLIC_API_URL,Array.from({length:48},(e,r)=>{let t=Math.floor(r/2),n=r%2==0?"00":"30",a="".concat(t.toString().padStart(2,"0"),":").concat(n),s=0===t?12:t>12?t-12:t,o=t<12?"AM":"PM";return{value:a,label:"".concat(s,":").concat(n," ").concat(o)}});let s={PASSWORD_MIN_LENGTH:8,NAME_MIN_LENGTH:2,NAME_MAX_LENGTH:50,DESCRIPTION_MAX_LENGTH:1e3,NOTES_MAX_LENGTH:2e3};function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}s.PASSWORD_MIN_LENGTH,s.NAME_MIN_LENGTH,s.NAME_MAX_LENGTH,s.DESCRIPTION_MAX_LENGTH,s.NOTES_MAX_LENGTH;let i={capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),capitalizeWords:e=>e.replace(/\w\S*/g,e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()),truncate:function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length<=r?e:e.substring(0,r)+t},slugify:e=>e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),initials:e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2),formatPhoneNumber:e=>{let r=e.replace(/\D/g,"");return 10===r.length?"(".concat(r.slice(0,3),") ").concat(r.slice(3,6),"-").concat(r.slice(6)):e},maskEmail:e=>{let[r,t]=e.split("@");if(r.length<=2)return e;let n=r.charAt(0)+"*".repeat(r.length-2)+r.charAt(r.length-1);return"".concat(n,"@").concat(t)}}},3294:(e,r,t)=>{t.d(r,{As:()=>c,BG:()=>u,nc:()=>l});var n=t(5453),a=t(6786),s=t(9385),o=t(2108);let i={[s.g.PATIENT]:["appointments:read:own","appointments:create:own","appointments:update:own","documents:read:own","profile:read:own","profile:update:own"],[s.g.DENTIST]:["appointments:read:all","appointments:create:all","appointments:update:all","appointments:delete:all","patients:read:all","patients:update:all","documents:read:all","documents:create:all","documents:update:all","documents:delete:all","profile:read:own","profile:update:own"],[s.g.ADMIN]:["*"]},l=(0,n.v)()((0,a.Zr)((e,r)=>({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:r=>{e({user:r,isAuthenticated:!!r,error:null})},setSession:r=>{e({session:r,user:(null==r?void 0:r.user)||null,isAuthenticated:!!(null==r?void 0:r.user),error:null})},setLoading:r=>{e({isLoading:r})},setError:r=>{e({error:r,isLoading:!1})},login:async(e,t)=>{let{setLoading:n,setError:a,setSession:s}=r();try{n(!0),a(null);let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok)throw Error("Login failed");let o=await r.json();if(o.success&&o.user&&o.token){let e={user:o.user,accessToken:o.token,expiresAt:new Date(Date.now()+864e5)};s(e)}else throw Error(o.message||"Login failed")}catch(e){a(e instanceof Error?e.message:"Login failed")}finally{n(!1)}},logout:async()=>{try{await (0,o.signOut)({redirect:!1})}catch(e){console.error("Logout error:",e)}e({user:null,session:null,isAuthenticated:!1,error:null})},refreshToken:async()=>{let{session:e,setSession:t,setError:n}=r();if(null==e?void 0:e.refreshToken)try{let r=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken})});if(!r.ok)throw Error("Token refresh failed");let n=await r.json();if(n.success&&n.token){let r={...e,accessToken:n.token,expiresAt:new Date(Date.now()+864e5)};t(r)}else throw Error("Token refresh failed")}catch(e){n(e instanceof Error?e.message:"Token refresh failed"),r().logout()}},updateProfile:async e=>{let{user:t,session:n,setUser:a,setError:s,setLoading:o}=r();if(!t||!n)return void s("User not authenticated");try{o(!0),s(null);let r=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(n.accessToken)},body:JSON.stringify(e)});if(!r.ok)throw Error("Profile update failed");let t=await r.json();if(t.success&&t.user)a(t.user);else throw Error(t.message||"Profile update failed")}catch(e){s(e instanceof Error?e.message:"Profile update failed")}finally{o(!1)}},checkPermission:e=>{let{user:t}=r();if(!t)return!1;let n=i[t.role]||[];return!!n.includes("*")||n.includes(e)},hasRole:e=>{let{user:t}=r();return(null==t?void 0:t.role)===e},clearError:()=>{e({error:null})},reset:()=>{e({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null})}}),{name:"auth-storage",storage:(0,a.KU)(()=>localStorage),partialize:e=>({user:e.user,session:e.session,isAuthenticated:e.isAuthenticated})})),c=()=>{let e=l();return{user:e.user,session:e.session,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error}},u=()=>{let e=l();return{login:e.login,logout:e.logout,refreshToken:e.refreshToken,updateProfile:e.updateProfile,checkPermission:e.checkPermission,hasRole:e.hasRole,clearError:e.clearError,reset:e.reset}}},9385:(e,r,t)=>{t.d(r,{g:()=>n});var n=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})}}]);