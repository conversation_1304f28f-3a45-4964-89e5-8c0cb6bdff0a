(()=>{var e={};e.id=758,e.ids=[758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36092:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>d});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(82243);let p=[{id:"1",email:"<EMAIL>",password:"password123",name:"Test Patient",role:u.g.PATIENT,image:null},{id:"2",email:"<EMAIL>",password:"password123",name:"Dr. Test Dentist",role:u.g.DENTIST,image:null},{id:"3",email:"<EMAIL>",password:"password123",name:"Test Admin",role:u.g.ADMIN,image:null}];async function d(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return i.NextResponse.json({error:"Email and password are required"},{status:400});let s=p.find(e=>e.email===r);if(!s||s.password!==t)return i.NextResponse.json({error:"Invalid credentials"},{status:401});let{password:n,...a}=s;return i.NextResponse.json({success:!0,user:a,token:"mock-jwt-token"})}catch(e){return console.error("Login API error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:g}=l;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},82243:(e,r,t)=>{"use strict";t.d(r,{g:()=>s});var s=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580],()=>t(36092));module.exports=s})();