(()=>{var e={};e.id=305,e.ids=[305],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54290:(e,t,s)=>{Promise.resolve().then(s.bind(s,57152))},57152:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687);s(43210);var a=s(16189);let n=(0,s(62688).A)("shield-x",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m14.5 9.5-5 5",key:"17q4r4"}],["path",{d:"m9.5 9.5 5 5",key:"18nt4w"}]]);var o=s(28559),i=s(32192),d=s(29523),l=s(44493),c=s(99720);let u=()=>{let e=(0,a.useRouter)(),{user:t}=(0,c.As)();return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center p-6",children:(0,r.jsxs)(l.Zp,{className:"glass-card w-full max-w-md",children:[(0,r.jsxs)(l.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-destructive/20 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(n,{className:"w-8 h-8 text-destructive"})}),(0,r.jsx)(l.ZB,{className:"text-2xl font-bold text-foreground",children:"Access Denied"}),(0,r.jsx)(l.BT,{className:"text-muted-foreground",children:"You don't have permission to access this page."})]}),(0,r.jsxs)(l.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:(0,r.jsx)("p",{children:"This page requires specific permissions that your account doesn't have. Please contact your administrator if you believe this is an error."})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)(d.$,{variant:"outline",onClick:()=>{e.back()},className:"flex-1",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Go Back"]}),(0,r.jsxs)(d.$,{onClick:()=>{if(t)switch(t.role){case"PATIENT":e.push("/patient/dashboard");break;case"DENTIST":e.push("/dentist/dashboard");break;case"ADMIN":e.push("/admin/dashboard");break;default:e.push("/dashboard")}else e.push("/")},className:"flex-1 glass-button",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Home"]})]}),t&&(0,r.jsxs)("div",{className:"mt-6 p-3 bg-muted/50 rounded-lg text-center",children:[(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Logged in as: ",(0,r.jsx)("span",{className:"font-medium",children:t.email})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Role: ",(0,r.jsx)("span",{className:"font-medium capitalize",children:t.role.toLowerCase()})]})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},77665:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\app\\\\unauthorized\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\unauthorized\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80641:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let l={children:["",{children:["unauthorized",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77665)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\unauthorized\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\unauthorized\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/unauthorized/page",pathname:"/unauthorized",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91242:(e,t,s)=>{Promise.resolve().then(s.bind(s,77665))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[243,97,658,513],()=>s(80641));module.exports=r})();