(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{285:(e,r,a)=>{"use strict";a.d(r,{$:()=>l});var t=a(5155);a(2115);var s=a(9708),o=a(2085),n=a(2911);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:a,size:o,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:o,className:r})),...c})}},2911:(e,r,a)=>{"use strict";a.d(r,{cn:()=>n,DR:()=>i});var t=a(2596),s=a(9688);a(9509).env.NEXT_PUBLIC_API_URL,Array.from({length:48},(e,r)=>{let a=Math.floor(r/2),t=r%2==0?"00":"30",s="".concat(a.toString().padStart(2,"0"),":").concat(t),o=0===a?12:a>12?a-12:a,n=a<12?"AM":"PM";return{value:s,label:"".concat(o,":").concat(t," ").concat(n)}});let o={PASSWORD_MIN_LENGTH:8,NAME_MIN_LENGTH:2,NAME_MAX_LENGTH:50,DESCRIPTION_MAX_LENGTH:1e3,NOTES_MAX_LENGTH:2e3};function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}o.PASSWORD_MIN_LENGTH,o.NAME_MIN_LENGTH,o.NAME_MAX_LENGTH,o.DESCRIPTION_MAX_LENGTH,o.NOTES_MAX_LENGTH;let i={capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),capitalizeWords:e=>e.replace(/\w\S*/g,e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()),truncate:function(e,r){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"...";return e.length<=r?e:e.substring(0,r)+a},slugify:e=>e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),initials:e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2),formatPhoneNumber:e=>{let r=e.replace(/\D/g,"");return 10===r.length?"(".concat(r.slice(0,3),") ").concat(r.slice(3,6),"-").concat(r.slice(6)):e},maskEmail:e=>{let[r,a]=e.split("@");if(r.length<=2)return e;let t=r.charAt(0)+"*".repeat(r.length-2)+r.charAt(r.length-1);return"".concat(t,"@").concat(a)}}},6117:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>h});var t=a(5155),s=a(2115),o=a(5695),n=a(6874),i=a.n(n),l=a(2177),c=a(8778),d=a(6671),u=a(2108),g=a(1929),p=a(6434),m=a(742);let h=()=>{var e,r,a,n,h,v,f,b;let y=(0,o.useRouter)(),[N,x]=(0,s.useState)(!1),[w,C]=(0,s.useState)(!1),{register:E,handleSubmit:_,formState:{errors:A},setValue:T,watch:P}=(0,l.mN)({resolver:(0,c.u)(m.zK),defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"",phone:"",acceptTerms:!1}}),j=P(),k=async e=>{try{x(!0),console.log("Registration data:",e),d.oR.success("Registration successful!",{description:"Please check your email to verify your account."}),y.push("/auth/login?message=registration-success")}catch(e){d.oR.error("Registration failed",{description:e instanceof Error?e.message:"Please try again."})}finally{x(!1)}},S=async()=>{try{C(!0);let e=await (0,u.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});(null==e?void 0:e.error)?d.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."}):(null==e?void 0:e.url)&&(d.oR.success("Registration successful!",{description:"Welcome to DentCare Pro!"}),y.push(e.url))}catch(e){d.oR.error("OAuth registration failed",{description:"Please try again or use the registration form."})}finally{C(!1)}};return(0,t.jsx)(g.A,{title:"Create your account",subtitle:"Join DentCare Pro and start managing your dental care",children:(0,t.jsxs)("form",{onSubmit:_(k),className:"space-y-6",children:[(0,t.jsx)(p.ID,{provider:"google",isLoading:w,disabled:N||w,onClick:S,children:"Sign up with Google"}),(0,t.jsx)(p.Sq,{text:"or sign up with email"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsx)(p.vy,{label:"First name",type:"text",placeholder:"Enter your first name",value:j.firstName,onChange:e=>T("firstName",e),error:null==(e=A.firstName)?void 0:e.message,required:!0,autoComplete:"given-name"}),(0,t.jsx)(p.vy,{label:"Last name",type:"text",placeholder:"Enter your last name",value:j.lastName,onChange:e=>T("lastName",e),error:null==(r=A.lastName)?void 0:r.message,required:!0,autoComplete:"family-name"})]}),(0,t.jsx)(p.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:j.email,onChange:e=>T("email",e),error:null==(a=A.email)?void 0:a.message,required:!0,autoComplete:"email"}),(0,t.jsx)(p.vy,{label:"Phone number",type:"tel",placeholder:"Enter your phone number (optional)",value:j.phone||"",onChange:e=>T("phone",e),error:null==(n=A.phone)?void 0:n.message,autoComplete:"tel"}),(0,t.jsx)(p.QT,{value:j.role,onChange:e=>T("role",e),error:null==(h=A.role)?void 0:h.message,disabled:N||w}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(p.vy,{label:"Password",type:"password",placeholder:"Create a strong password",value:j.password,onChange:e=>T("password",e),error:null==(v=A.password)?void 0:v.message,required:!0,autoComplete:"new-password"}),(0,t.jsx)(p.vy,{label:"Confirm password",type:"password",placeholder:"Confirm your password",value:j.confirmPassword,onChange:e=>T("confirmPassword",e),error:null==(f=A.confirmPassword)?void 0:f.message,required:!0,autoComplete:"new-password"})]}),(0,t.jsx)(p.PD,{checked:j.acceptTerms,onChange:e=>T("acceptTerms",e),error:null==(b=A.acceptTerms)?void 0:b.message,disabled:N||w}),(0,t.jsx)(p.FX,{isLoading:N,disabled:N||w,loadingText:"Creating account...",children:"Create account"}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Already have an account?"," ",(0,t.jsx)(i(),{href:"/auth/login",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign in"})]})})]})})}},8433:(e,r,a)=>{Promise.resolve().then(a.bind(a,6117))},9385:(e,r,a)=>{"use strict";a.d(r,{g:()=>t});var t=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})}},e=>{var r=r=>e(e.s=r);e.O(0,[52,919,3,981,571,441,684,358],()=>r(8433)),_N_E=e.O()}]);