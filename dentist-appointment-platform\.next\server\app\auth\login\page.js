(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49457:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(60687),a=t(43210),o=t(16189),n=t(85814),i=t.n(n),d=t(27605),l=t(57335),c=t(52581),p=t(82136),u=t(31923),m=t(13977),h=t(99720),g=t(35810);let x=()=>{let e=(0,o.useRouter)(),{login:r}=(0,h.BG)(),[t,n]=(0,a.useState)(!1),[x,f]=(0,a.useState)(!1),{register:v,handleSubmit:y,formState:{errors:b},setValue:j,watch:w}=(0,d.mN)({resolver:(0,l.u)(g.X5),defaultValues:{email:"",password:""}}),P=w(),C=async r=>{try{n(!0);let t=await (0,p.signIn)("credentials",{email:r.email,password:r.password,redirect:!1});t?.error?c.oR.error("Login failed",{description:"Please check your credentials and try again."}):(c.oR.success("Login successful!",{description:"Welcome back to DentCare Pro."}),e.push("/dashboard"))}catch(e){c.oR.error("Login failed",{description:e instanceof Error?e.message:"Please check your credentials and try again."})}finally{n(!1)}},k=async()=>{try{f(!0);let r=await (0,p.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});r?.error?c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."}):r?.url&&(c.oR.success("Login successful!",{description:"Redirecting to dashboard..."}),e.push(r.url))}catch(e){c.oR.error("OAuth login failed",{description:"Please try again or use email/password login."})}finally{f(!1)}};return(0,s.jsx)(u.A,{title:"Welcome back",subtitle:"Sign in to your DentCare Pro account",children:(0,s.jsxs)("form",{onSubmit:y(C),className:"space-y-6",children:[(0,s.jsx)(m.vy,{label:"Email address",type:"email",placeholder:"Enter your email",value:P.email,onChange:e=>j("email",e),error:b.email?.message,required:!0,autoComplete:"email"}),(0,s.jsx)(m.vy,{label:"Password",type:"password",placeholder:"Enter your password",value:P.password,onChange:e=>j("password",e),error:b.password?.message,required:!0,autoComplete:"current-password"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"Forgot your password?"})}),(0,s.jsx)(m.FX,{isLoading:t,disabled:t||x,loadingText:"Signing in...",children:"Sign in"}),(0,s.jsx)(m.Sq,{}),(0,s.jsx)(m.ID,{provider:"google",isLoading:x,disabled:t||x,onClick:k}),(0,s.jsxs)("div",{className:"p-4 bg-muted/50 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-foreground mb-2",children:"Test Credentials"}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Patient:"})," <EMAIL> / password123"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Dentist:"})," <EMAIL> / password123"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Admin:"})," <EMAIL> / password123"]})]})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)(i(),{href:"/auth/register",className:"text-primary hover:text-primary/80 transition-colors font-medium",children:"Sign up"})]})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65385:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81351)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\login\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},70757:(e,r,t)=>{Promise.resolve().then(t.bind(t,81351))},79551:e=>{"use strict";e.exports=require("url")},81351:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\auth\\login\\page.tsx","default")},83909:(e,r,t)=>{Promise.resolve().then(t.bind(t,49457))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,97,658,665,513,383],()=>t(65385));module.exports=s})();