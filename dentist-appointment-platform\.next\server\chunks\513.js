exports.id=513,exports.ids=[513],exports.modules={13403:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},24019:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(60687);s(43210);var n=s(8730),a=s(24224),o=s(60140);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:a=!1,...l}){let c=a?n.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:s,className:e})),...l})}},37062:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),n=s(43210),a=s(99720),o=s(53881);let i=({children:e})=>{let{setUser:t,setSession:s}=(0,a.nc)();return(0,n.useEffect)(()=>{let e={id:"mock-user-1",email:"<EMAIL>",name:"Dr. John Smith",image:null,role:o.g.DENTIST,createdAt:new Date,updatedAt:new Date,dentistProfile:{id:"dentist-profile-1",userId:"mock-user-1",licenseNumber:"DDS-12345",specialization:["General Dentistry","Cosmetic Dentistry"],workingHours:{monday:[{start:"09:00",end:"17:00"}],tuesday:[{start:"09:00",end:"17:00"}],wednesday:[{start:"09:00",end:"17:00"}],thursday:[{start:"09:00",end:"17:00"}],friday:[{start:"09:00",end:"17:00"}],saturday:[],sunday:[]},practiceInfo:{name:"Smith Dental Practice",address:"123 Main St, City, State 12345",phone:"(*************",email:"<EMAIL>",website:"https://smithdental.com"},createdAt:new Date,updatedAt:new Date,user:{}}},r={user:e,accessToken:"mock-access-token",expiresAt:new Date(Date.now()+864e5)};t(e),s(r)},[t,s]),(0,r.jsx)(r.Fragment,{children:e})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>o});var r=s(60687);s(43210);var n=s(60140);function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},47329:(e,t,s)=>{"use strict";s.d(t,{default:()=>er});var r=s(60687),n=s(43210),a=s(16189),o=s(60140),i=s(65106),l=s(99720),c=s(53881),d=s(85814),m=s.n(d),u=s(29523),p=s(96834),h=s(32192),f=s(40228),x=s(6727),g=s(41312),v=s(58869),b=s(10022),N=s(58887),j=s(53411),w=s(60137),A=s(99891),y=s(84027),k=s(65668),T=s(14952),D=s(47033);let C=[{name:"Dashboard",href:"/dashboard",icon:h.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN]},{name:"Appointments",href:"/appointments",icon:f.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN],children:[{name:"My Appointments",href:"/appointments",icon:f.A,roles:[c.g.PATIENT]},{name:"Book Appointment",href:"/appointments/book",icon:f.A,roles:[c.g.PATIENT]},{name:"All Appointments",href:"/appointments",icon:f.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Schedule",href:"/appointments/schedule",icon:x.A,roles:[c.g.DENTIST,c.g.ADMIN]}]},{name:"Patients",href:"/patients",icon:g.A,roles:[c.g.DENTIST,c.g.ADMIN],children:[{name:"All Patients",href:"/patients",icon:g.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Add Patient",href:"/patients/add",icon:v.A,roles:[c.g.DENTIST,c.g.ADMIN]}]},{name:"Documents",href:"/documents",icon:b.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN],children:[{name:"My Documents",href:"/documents",icon:b.A,roles:[c.g.PATIENT]},{name:"Patient Documents",href:"/documents",icon:b.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Upload Document",href:"/documents/upload",icon:b.A,roles:[c.g.DENTIST,c.g.ADMIN]}]},{name:"Profile",href:"/profile",icon:v.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN]},{name:"Messages",href:"/messages",icon:N.A,badge:3,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN]},{name:"Reports",href:"/reports",icon:j.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Practice Management",href:"/practice",icon:w.A,roles:[c.g.DENTIST,c.g.ADMIN],children:[{name:"Working Hours",href:"/practice/hours",icon:f.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Services",href:"/practice/services",icon:x.A,roles:[c.g.DENTIST,c.g.ADMIN]},{name:"Staff",href:"/practice/staff",icon:g.A,roles:[c.g.ADMIN]}]},{name:"Administration",href:"/admin",icon:A.A,roles:[c.g.ADMIN],children:[{name:"User Management",href:"/admin/users",icon:g.A,roles:[c.g.ADMIN]},{name:"System Settings",href:"/admin/settings",icon:y.A,roles:[c.g.ADMIN]},{name:"Audit Logs",href:"/admin/audit",icon:b.A,roles:[c.g.ADMIN]}]}],E=[{name:"Settings",href:"/settings",icon:y.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN]},{name:"Help & Support",href:"/help",icon:k.A,roles:[c.g.PATIENT,c.g.DENTIST,c.g.ADMIN]}],S=()=>{let e=(0,a.usePathname)(),{sidebarCollapsed:t,sidebarOpen:s,setSidebarOpen:n,toggleSidebar:c}=(0,i.cL)(),{user:d}=(0,l.As)(),h=e=>d?e.filter(e=>!!(!e.roles||e.roles.includes(d.role))&&(e.children&&(e.children=h(e.children)),!0)):[],f=h(C),x=h(E),g=t=>"/dashboard"===t?"/dashboard"===e||"/"===e:e?.startsWith(t),v=({item:e,level:s=0})=>{let a=g(e.href),i=e.children&&e.children.length>0,l=i&&e.children.some(e=>g(e.href));return(0,r.jsxs)("div",{children:[(0,r.jsxs)(m(),{href:e.href,className:(0,o.cn)("group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground",s>0&&"ml-4 pl-6",a||l?"bg-accent text-accent-foreground":"text-muted-foreground",t&&0===s&&"justify-center px-2"),onClick:()=>{window.innerWidth<768&&n(!1)},children:[(0,r.jsx)(e.icon,{className:(0,o.cn)("h-5 w-5 shrink-0",t&&0===s?"mr-0":"mr-3")}),(!t||s>0)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"truncate",children:e.name}),e.badge&&(0,r.jsx)(p.E,{variant:"secondary",className:"ml-auto h-5 w-5 rounded-full p-0 text-xs",children:e.badge})]})]}),i&&(!t||s>0)&&(0,r.jsx)("div",{className:"mt-1 space-y-1",children:e.children.map(e=>(0,r.jsx)(v,{item:e,level:s+1},e.href))})]})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:(0,o.cn)("fixed left-0 top-0 z-50 h-full bg-background border-r border-border transition-all duration-300 ease-in-out hidden md:flex flex-col",t?"w-20":"w-72"),children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-border",children:[!t&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,r.jsx)("span",{className:"text-lg font-semibold",children:"DentistApp"})]}),(0,r.jsx)(u.$,{variant:"ghost",size:"icon",onClick:c,className:"h-8 w-8",children:t?(0,r.jsx)(T.A,{className:"h-4 w-4"}):(0,r.jsx)(D.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 p-4 overflow-y-auto",children:f.map(e=>(0,r.jsx)(v,{item:e},e.href))}),(0,r.jsx)("div",{className:"border-t border-border p-4 space-y-2",children:x.map(e=>(0,r.jsx)(v,{item:e},e.href))})]}),(0,r.jsxs)("div",{className:(0,o.cn)("fixed left-0 top-0 z-50 h-full w-72 bg-background border-r border-border transition-transform duration-300 ease-in-out md:hidden flex flex-col",s?"translate-x-0":"-translate-x-full"),children:[(0,r.jsx)("div",{className:"flex h-16 items-center px-4 border-b border-border",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,r.jsx)("span",{className:"text-lg font-semibold",children:"DentistApp"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 p-4 overflow-y-auto",children:f.map(e=>(0,r.jsx)(v,{item:e},e.href))}),(0,r.jsx)("div",{className:"border-t border-border p-4 space-y-2",children:x.map(e=>(0,r.jsx)(v,{item:e},e.href))})]})]})};var I=s(40591);function P({...e}){return(0,r.jsx)(I.bL,{"data-slot":"dropdown-menu",...e})}function L({...e}){return(0,r.jsx)(I.l9,{"data-slot":"dropdown-menu-trigger",...e})}function M({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(I.ZL,{children:(0,r.jsx)(I.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function R({className:e,inset:t,variant:s="default",...n}){return(0,r.jsx)(I.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function $({className:e,inset:t,...s}){return(0,r.jsx)(I.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...s})}function _({className:e,...t}){return(0,r.jsx)(I.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var z=s(11096);function U({className:e,...t}){return(0,r.jsx)(z.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function O({className:e,...t}){return(0,r.jsx)(z._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",e),...t})}function H({className:e,...t}){return(0,r.jsx)(z.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var B=s(12941),G=s(99270),W=s(97051),F=s(21134),X=s(363),Z=s(34318),J=s(40083);let V=()=>{let e=(0,a.useRouter)(),{toggleSidebar:t,setSidebarOpen:s}=(0,i.cL)(),{pageTitle:n}=(0,i.Sw)(),{notifications:c,clearNotifications:d}=(0,i.E$)(),{user:m,isAuthenticated:h}=(0,l.As)(),{logout:f}=(0,l.BG)();if(!h||!m)return null;let x=c.filter(e=>!e.action),g=async()=>{await f(),e.push("/")},b=()=>{e.push("/notifications")};return(0,r.jsx)("header",{className:"sticky top-0 z-30 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>s(!0),children:[(0,r.jsx)(B.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]}),(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"hidden md:flex",onClick:t,children:[(0,r.jsx)(B.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:n})})]}),(0,r.jsx)("div",{className:"hidden lg:flex flex-1 max-w-md mx-8",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(G.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)("input",{type:"search",placeholder:"Search...",className:"w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"lg:hidden",children:[(0,r.jsx)(G.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Search"})]}),(0,r.jsxs)(P,{children:[(0,r.jsx)(L,{asChild:!0,children:(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,r.jsx)(W.A,{className:"h-5 w-5"}),x.length>0&&(0,r.jsx)(p.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs",children:x.length>9?"9+":x.length}),(0,r.jsx)("span",{className:"sr-only",children:"Notifications"})]})}),(0,r.jsxs)(M,{align:"end",className:"w-80",children:[(0,r.jsxs)($,{className:"flex items-center justify-between",children:["Notifications",c.length>0&&(0,r.jsx)(u.$,{variant:"ghost",size:"sm",onClick:d,className:"h-auto p-0 text-xs text-muted-foreground hover:text-foreground",children:"Clear all"})]}),(0,r.jsx)(_,{}),0===c.length?(0,r.jsx)("div",{className:"p-4 text-center text-sm text-muted-foreground",children:"No notifications"}):(0,r.jsxs)("div",{className:"max-h-64 overflow-y-auto",children:[c.slice(0,5).map(e=>(0,r.jsx)(R,{className:"flex flex-col items-start p-3 cursor-pointer",onClick:b,children:(0,r.jsxs)("div",{className:"flex w-full items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.message&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:o.DR.truncate(e.message,60)})]}),(0,r.jsx)(p.E,{variant:"error"===e.type?"destructive":"warning"===e.type?"secondary":"default",className:"ml-2 text-xs",children:e.type})]})},e.id)),c.length>5&&(0,r.jsx)(R,{className:"text-center text-sm text-primary",onClick:b,children:"View all notifications"})]})]})]}),(0,r.jsxs)(P,{children:[(0,r.jsx)(L,{asChild:!0,children:(0,r.jsx)(u.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,r.jsxs)(U,{className:"h-10 w-10",children:[(0,r.jsx)(O,{src:m?.image,alt:m?.name||"User"}),(0,r.jsx)(H,{className:"bg-primary/10 text-primary",children:m?.name?o.DR.initials(m.name):"U"})]})})}),(0,r.jsxs)(M,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)($,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:m?.name||"User"}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:m?.email}),(0,r.jsx)(p.E,{variant:"outline",className:"w-fit text-xs mt-1",children:o.DR.capitalizeWords(m?.role.toLowerCase()||"")})]})}),(0,r.jsx)(_,{}),(0,r.jsxs)(R,{onClick:()=>{e.push("/profile")},children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]}),(0,r.jsxs)(R,{onClick:()=>{e.push("/settings")},children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsxs)(R,{onClick:()=>e.push("/help"),children:[(0,r.jsx)(k.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Help & Support"})]}),(0,r.jsx)(_,{}),(0,r.jsx)($,{className:"text-xs text-muted-foreground",children:"Theme"}),(0,r.jsxs)(R,{children:[(0,r.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Light"})]}),(0,r.jsxs)(R,{children:[(0,r.jsx)(X.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dark"})]}),(0,r.jsxs)(R,{children:[(0,r.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"System"})]}),(0,r.jsx)(_,{}),(0,r.jsxs)(R,{onClick:g,className:"text-destructive",children:[(0,r.jsx)(J.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]})]})]})})},q=({items:e,className:t,separator:s=(0,r.jsx)(T.A,{className:"h-4 w-4 text-muted-foreground"}),showHome:n=!0,maxItems:i=5})=>{let l=(0,a.useRouter)(),c=n&&e[0]?.label!=="Home"?[{label:"Home",href:"/dashboard",icon:h.A},...e]:e,d=c.length>i?[c[0],{label:"...",href:void 0},...c.slice(-i+2)]:c,u=(e,t)=>{e.href&&!e.current&&l.push(e.href)};return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,o.cn)("flex items-center space-x-1 text-sm",t),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let n=t===d.length-1,a=e.current||n,i="..."===e.label;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)("span",{className:"mx-2 flex-shrink-0",children:s}),i?(0,r.jsx)("span",{className:"text-muted-foreground px-2",children:"..."}):(0,r.jsx)("div",{className:"flex items-center",children:e.href&&!a?(0,r.jsxs)(m(),{href:e.href,className:(0,o.cn)("flex items-center space-x-1 rounded-md px-2 py-1 transition-colors hover:bg-accent hover:text-accent-foreground","text-muted-foreground hover:text-foreground"),onClick:s=>{s.preventDefault(),u(e,t)},children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate max-w-[150px] sm:max-w-[200px]",children:e.label})]}):(0,r.jsxs)("span",{className:(0,o.cn)("flex items-center space-x-1 px-2 py-1 rounded-md",a?"text-foreground font-medium bg-accent/50":"text-muted-foreground"),"aria-current":a?"page":void 0,children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate max-w-[150px] sm:max-w-[200px]",children:e.label})]})})]},`${e.label}-${t}`)})})})};var K=s(44493),Q=s(43649),Y=s(78122),ee=s(48210);class et extends n.Component{constructor(e){super(e),this.logErrorToService=(e,t)=>{console.log("Error logged:",{message:e.message,stack:e.stack,componentStack:t.componentStack,errorId:this.state.errorId,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href})},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null,errorId:null})},this.handleReload=()=>{window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.copyErrorDetails=()=>{let{error:e,errorInfo:t,errorId:s}=this.state,r={errorId:s,message:e?.message,stack:e?.stack,componentStack:t?.componentStack,timestamp:new Date().toISOString(),url:window.location.href};navigator.clipboard.writeText(JSON.stringify(r,null,2))},this.state={hasError:!1,error:null,errorInfo:null,errorId:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t}),this.props.onError&&this.props.onError(e,t),this.logErrorToService(e,t)}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let{error:e,errorInfo:t,errorId:s}=this.state,{level:n="component",showDetails:a=!1}=this.props;return"critical"===n?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,r.jsxs)(K.Zp,{className:"w-full max-w-2xl glass-card",children:[(0,r.jsxs)(K.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10",children:(0,r.jsx)(Q.A,{className:"h-8 w-8 text-destructive"})}),(0,r.jsx)(K.ZB,{className:"text-2xl",children:"Critical Error"}),(0,r.jsx)(K.BT,{children:"A critical error has occurred that prevents the application from functioning properly."})]}),(0,r.jsxs)(K.Wu,{className:"space-y-4",children:[a&&e&&(0,r.jsxs)("div",{className:"rounded-md bg-muted p-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:e.message}),s&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Error ID: ",s]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)(u.$,{onClick:this.handleReload,className:"flex-1",children:[(0,r.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),"Reload Page"]}),(0,r.jsxs)(u.$,{variant:"outline",onClick:this.handleGoHome,className:"flex-1",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Go Home"]}),a&&(0,r.jsxs)(u.$,{variant:"outline",onClick:this.copyErrorDetails,children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),"Copy Details"]})]})]})]})}):"page"===n?(0,r.jsx)("div",{className:"flex min-h-[400px] items-center justify-center p-4",children:(0,r.jsxs)(K.Zp,{className:"w-full max-w-lg glass-card",children:[(0,r.jsxs)(K.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10",children:(0,r.jsx)(Q.A,{className:"h-6 w-6 text-destructive"})}),(0,r.jsx)(K.ZB,{children:"Something went wrong"}),(0,r.jsx)(K.BT,{children:"We encountered an error while loading this page."})]}),(0,r.jsxs)(K.Wu,{className:"space-y-4",children:[a&&e&&(0,r.jsxs)("div",{className:"rounded-md bg-muted p-3",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:e.message}),s&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Error ID: ",s]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(u.$,{onClick:this.handleRetry,className:"flex-1",children:[(0,r.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),"Try Again"]}),(0,r.jsxs)(u.$,{variant:"outline",onClick:this.handleGoHome,children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Home"]})]})]})]})}):(0,r.jsx)("div",{className:"rounded-md border border-destructive/20 bg-destructive/5 p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(Q.A,{className:"h-5 w-5 text-destructive mt-0.5"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-destructive",children:"Component Error"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This component failed to render properly."}),a&&e&&(0,r.jsxs)("details",{className:"text-xs",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-muted-foreground hover:text-foreground",children:"Show details"}),(0,r.jsx)("pre",{className:"mt-2 whitespace-pre-wrap font-mono text-xs bg-muted p-2 rounded",children:e.message})]}),(0,r.jsxs)(u.$,{size:"sm",variant:"outline",onClick:this.handleRetry,children:[(0,r.jsx)(Y.A,{className:"mr-1 h-3 w-3"}),"Retry"]})]})]})})}return this.props.children}}var es=s(64616);let er=({children:e})=>{let t=(0,a.usePathname)(),{sidebarCollapsed:s,sidebarOpen:c,setSidebarOpen:d}=(0,i.cL)(),{pageTitle:m,breadcrumbs:u}=(0,i.Sw)(),{user:p,isAuthenticated:h}=(0,l.As)(),[f,x]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{let e=()=>{x(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,n.useEffect)(()=>{f&&d(!1)},[t,f,d]),t?.startsWith("/auth")||"/login"===t||"/register"===t||!h)?(0,r.jsx)(et,{level:"critical",children:(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[e,(0,r.jsx)(es.Toaster,{})]})}):(0,r.jsx)(et,{level:"critical",children:(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsx)(S,{}),f&&c&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden",onClick:()=>d(!1)}),(0,r.jsxs)("div",{className:(0,o.cn)("flex flex-col min-h-screen transition-all duration-300 ease-in-out",f?"ml-0":s?"ml-20":"ml-72"),children:[(0,r.jsx)(V,{}),(0,r.jsxs)("main",{className:"flex-1 flex flex-col",children:[u.length>0&&(0,r.jsx)("div",{className:"border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-3",children:(0,r.jsx)(q,{items:u})})}),(0,r.jsx)("div",{className:"flex-1 container mx-auto px-4 py-6",children:(0,r.jsx)(et,{level:"page",children:e})})]})]}),(0,r.jsx)(es.Toaster,{})]})})}},48482:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\ui\\sonner.tsx","Toaster")},53881:(e,t,s)=>{"use strict";s.d(t,{g:()=>r});var r=function(e){return e.PATIENT="PATIENT",e.DENTIST="DENTIST",e.ADMIN="ADMIN",e}({})},60140:(e,t,s)=>{"use strict";s.d(t,{cn:()=>o,DR:()=>i});var r=s(49384),n=s(82348);process.env.NEXT_PUBLIC_API_URL,Array.from({length:48},(e,t)=>{let s=Math.floor(t/2),r=t%2==0?"00":"30",n=`${s.toString().padStart(2,"0")}:${r}`,a=0===s?12:s>12?s-12:s,o=s<12?"AM":"PM";return{value:n,label:`${a}:${r} ${o}`}});let a={PASSWORD_MIN_LENGTH:8,NAME_MIN_LENGTH:2,NAME_MAX_LENGTH:50,DESCRIPTION_MAX_LENGTH:1e3,NOTES_MAX_LENGTH:2e3};function o(...e){return(0,n.QP)((0,r.$)(e))}a.PASSWORD_MIN_LENGTH,a.NAME_MIN_LENGTH,a.NAME_MAX_LENGTH,a.DESCRIPTION_MAX_LENGTH,a.NOTES_MAX_LENGTH;let i={capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),capitalizeWords:e=>e.replace(/\w\S*/g,e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()),truncate:(e,t,s="...")=>e.length<=t?e:e.substring(0,t)+s,slugify:e=>e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),initials:e=>e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2),formatPhoneNumber:e=>{let t=e.replace(/\D/g,"");return 10===t.length?`(${t.slice(0,3)}) ${t.slice(3,6)}-${t.slice(6)}`:e},maskEmail:e=>{let[t,s]=e.split("@");if(t.length<=2)return e;let r=t.charAt(0)+"*".repeat(t.length-2)+t.charAt(t.length-1);return`${r}@${s}`}}},61135:()=>{},62789:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),n=s(82136),a=s(43210),o=s(99720);let i=()=>{let{data:e,status:t}=(0,n.useSession)(),{setUser:s,setSession:r,setLoading:i}=(0,o.nc)();return(0,a.useEffect)(()=>{if(i("loading"===t),"authenticated"===t&&e?.user){let t={id:e.user.id,email:e.user.email,name:e.user.name,role:e.user.role,image:e.user.image,createdAt:new Date,updatedAt:new Date},n={user:t,accessToken:e.accessToken,expiresAt:new Date(Date.now()+864e5)};s(t),r(n)}else"unauthenticated"===t&&(s(null),r(null))},[e,t,s,r,i]),null};function l({children:e}){return(0,r.jsxs)(n.SessionProvider,{children:[(0,r.jsx)(i,{}),e]})}},64616:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>o});var r=s(60687),n=s(10218),a=s(52581);let o=({...e})=>{let{theme:t="system"}=(0,n.D)();return(0,r.jsx)(a.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},65106:(e,t,s)=>{"use strict";s.d(t,{E$:()=>c,Sw:()=>d,cL:()=>l});var r=s(26787),n=s(59350);s(53881);var a=function(e){return e.SUCCESS="success",e.ERROR="error",e.WARNING="warning",e.INFO="info",e}({});let o=()=>`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,i=(0,r.v)()((0,n.Zr)((e,t)=>({theme:"dark",sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[],setTheme:t=>{e({theme:t})},toggleSidebar:()=>{e(e=>({sidebarCollapsed:!e.sidebarCollapsed}))},setSidebarOpen:t=>{e({sidebarOpen:t})},setSidebarCollapsed:t=>{e({sidebarCollapsed:t})},setGlobalLoading:(t,s="")=>{e({globalLoading:t,loadingMessage:s})},addNotification:s=>{let r={...s,id:o(),createdAt:new Date};e(e=>({notifications:[...e.notifications,r]})),r.duration&&r.duration>0&&setTimeout(()=>{t().removeNotification(r.id)},r.duration)},removeNotification:t=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==t)}))},clearNotifications:()=>{e({notifications:[]})},openModal:t=>{e(e=>({modals:{...e.modals,[t]:!0}}))},closeModal:t=>{e(e=>({modals:{...e.modals,[t]:!1}}))},toggleModal:t=>{e(e=>({modals:{...e.modals,[t]:!e.modals[t]}}))},isModalOpen:e=>t().modals[e]||!1,setPageTitle:t=>{e({pageTitle:t})},setBreadcrumbs:t=>{e({breadcrumbs:t})},addBreadcrumb:t=>{e(e=>({breadcrumbs:[...e.breadcrumbs,t]}))},reset:()=>{e({sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[]})}}),{name:"ui-storage",storage:(0,n.KU)(()=>localStorage),partialize:e=>({theme:e.theme,sidebarCollapsed:e.sidebarCollapsed})})),l=()=>{let e=i(e=>e.sidebarCollapsed),t=i(e=>e.sidebarOpen),s=i(e=>e.toggleSidebar);return{sidebarCollapsed:e,sidebarOpen:t,toggleSidebar:s,setSidebarOpen:i(e=>e.setSidebarOpen),setSidebarCollapsed:i(e=>e.setSidebarCollapsed)}},c=()=>{let e=i(e=>e.notifications),t=i(e=>e.addNotification),s=i(e=>e.removeNotification);return{notifications:e,addNotification:t,removeNotification:s,clearNotifications:i(e=>e.clearNotifications),showSuccess:(e,s,r=5e3)=>{t({type:a.SUCCESS,title:e,message:s,duration:r})},showError:(e,s,r=0)=>{t({type:a.ERROR,title:e,message:s,duration:r})},showWarning:(e,s,r=7e3)=>{t({type:a.WARNING,title:e,message:s,duration:r})},showInfo:(e,s,r=5e3)=>{t({type:a.INFO,title:e,message:s,duration:r})}}},d=()=>{let e=i(e=>e.pageTitle),t=i(e=>e.breadcrumbs),s=i(e=>e.setPageTitle);return{pageTitle:e,breadcrumbs:t,setPageTitle:s,setBreadcrumbs:i(e=>e.setBreadcrumbs),addBreadcrumb:i(e=>e.addBreadcrumb)}}},69032:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\components\\\\providers\\\\MockAuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\MockAuthProvider.tsx","default")},77726:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call RoleBasedLayout() from the server but RoleBasedLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","RoleBasedLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call PatientLayout() from the server but PatientLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","PatientLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call DentistLayout() from the server but DentistLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","DentistLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call AdminLayout() from the server but AdminLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","AdminLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call SimpleLayout() from the server but SimpleLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","SimpleLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call FullWidthLayout() from the server but FullWidthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","FullWidthLayout"),(0,r.registerClientReference)(function(){throw Error("Attempted to call CenteredLayout() from the server but CenteredLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","CenteredLayout");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\layout\\AppLayout.tsx","default")},83686:(e,t,s)=>{Promise.resolve().then(s.bind(s,47329)),Promise.resolve().then(s.bind(s,37062)),Promise.resolve().then(s.bind(s,62789)),Promise.resolve().then(s.bind(s,64616))},89953:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\components\\\\providers\\\\NextAuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\components\\providers\\NextAuthProvider.tsx","default")},91934:(e,t,s)=>{Promise.resolve().then(s.bind(s,77726)),Promise.resolve().then(s.bind(s,69032)),Promise.resolve().then(s.bind(s,89953)),Promise.resolve().then(s.bind(s,48482))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>h,viewport:()=>f});var r=s(37413),n=s(51194),a=s.n(n),o=s(84276),i=s.n(o),l=s(79123),c=s.n(l);s(61135);var d=s(77726),m=s(69032),u=s(89953),p=s(48482);let h={title:"Dentist Appointment Management Platform",description:"Modern, secure, and HIPAA-compliant dental practice management system",keywords:["dentist","appointment","healthcare","dental practice","patient management"],authors:[{name:"Dentist Appointment Platform Team"}],icons:{icon:"/favicon.ico",apple:"/apple-touch-icon.png"},openGraph:{title:"Dentist Appointment Management Platform",description:"Modern, secure, and HIPAA-compliant dental practice management system",type:"website",locale:"en_US"},robots:{index:!1,follow:!1}},f={width:"device-width",initialScale:1,themeColor:"#0a0a0b",colorScheme:"dark"};function x({children:e}){return(0,r.jsx)("html",{lang:"en",className:`dark ${a().variable} ${i().variable} ${c().variable}`,suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:"min-h-screen bg-background font-sans antialiased",children:(0,r.jsxs)("div",{id:"root",className:"relative flex min-h-screen flex-col",children:[(0,r.jsx)(u.default,{children:(0,r.jsx)(m.default,{children:(0,r.jsx)(d.default,{children:e})})}),(0,r.jsx)(p.Toaster,{})]})})})}},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(60687);s(43210);var n=s(8730),a=s(24224),o=s(60140);let i=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...a}){let l=s?n.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...a})}},99720:(e,t,s)=>{"use strict";s.d(t,{As:()=>c,BG:()=>d,nc:()=>l});var r=s(26787),n=s(59350),a=s(53881),o=s(82136);let i={[a.g.PATIENT]:["appointments:read:own","appointments:create:own","appointments:update:own","documents:read:own","profile:read:own","profile:update:own"],[a.g.DENTIST]:["appointments:read:all","appointments:create:all","appointments:update:all","appointments:delete:all","patients:read:all","patients:update:all","documents:read:all","documents:create:all","documents:update:all","documents:delete:all","profile:read:own","profile:update:own"],[a.g.ADMIN]:["*"]},l=(0,r.v)()((0,n.Zr)((e,t)=>({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setSession:t=>{e({session:t,user:t?.user||null,isAuthenticated:!!t?.user,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t,isLoading:!1})},login:async(e,s)=>{let{setLoading:r,setError:n,setSession:a}=t();try{r(!0),n(null);let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:s})});if(!t.ok)throw Error("Login failed");let o=await t.json();if(o.success&&o.user&&o.token){let e={user:o.user,accessToken:o.token,expiresAt:new Date(Date.now()+864e5)};a(e)}else throw Error(o.message||"Login failed")}catch(e){n(e instanceof Error?e.message:"Login failed")}finally{r(!1)}},logout:async()=>{try{await (0,o.signOut)({redirect:!1})}catch(e){console.error("Logout error:",e)}e({user:null,session:null,isAuthenticated:!1,error:null})},refreshToken:async()=>{let{session:e,setSession:s,setError:r}=t();if(e?.refreshToken)try{let t=await fetch("/api/auth/refresh",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken})});if(!t.ok)throw Error("Token refresh failed");let r=await t.json();if(r.success&&r.token){let t={...e,accessToken:r.token,expiresAt:new Date(Date.now()+864e5)};s(t)}else throw Error("Token refresh failed")}catch(e){r(e instanceof Error?e.message:"Token refresh failed"),t().logout()}},updateProfile:async e=>{let{user:s,session:r,setUser:n,setError:a,setLoading:o}=t();if(!s||!r)return void a("User not authenticated");try{o(!0),a(null);let t=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.accessToken}`},body:JSON.stringify(e)});if(!t.ok)throw Error("Profile update failed");let s=await t.json();if(s.success&&s.user)n(s.user);else throw Error(s.message||"Profile update failed")}catch(e){a(e instanceof Error?e.message:"Profile update failed")}finally{o(!1)}},checkPermission:e=>{let{user:s}=t();if(!s)return!1;let r=i[s.role]||[];return!!r.includes("*")||r.includes(e)},hasRole:e=>{let{user:s}=t();return s?.role===e},clearError:()=>{e({error:null})},reset:()=>{e({user:null,session:null,isAuthenticated:!1,isLoading:!1,error:null})}}),{name:"auth-storage",storage:(0,n.KU)(()=>localStorage),partialize:e=>({user:e.user,session:e.session,isAuthenticated:e.isAuthenticated})})),c=()=>{let e=l();return{user:e.user,session:e.session,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error}},d=()=>{let e=l();return{login:e.login,logout:e.logout,refreshToken:e.refreshToken,updateProfile:e.updateProfile,checkPermission:e.checkPermission,hasRole:e.hasRole,clearError:e.clearError,reset:e.reset}}}};