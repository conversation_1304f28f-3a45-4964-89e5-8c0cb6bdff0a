(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[764],{381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},446:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},786:e=>{e.exports={style:{fontFamily:"'JetBrains Mono', 'JetBrains Mono Fallback'",fontStyle:"normal"},className:"__className_3c557b",variable:"__variable_3c557b"}},1243:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1362:(e,t,n)=>{"use strict";n.d(t,{D:()=>l});var r=n(2115),o="(prefers-color-scheme: dark)",i=r.createContext(void 0),a={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=r.useContext(i))?e:a},u=null,c=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},s=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},d=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},1414:(e,t,n)=>{"use strict";e.exports=n(2436)},1497:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2098:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2305:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2436:(e,t,n)=>{"use strict";var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2713:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3509:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},3904:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4011:(e,t,n)=>{"use strict";n.d(t,{H4:()=>M,_V:()=>k,bL:()=>E});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),u=n(1414);function c(){return()=>{}}var s=n(5155),d="Avatar",[f,p]=(0,o.A)(d),[h,m]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=m(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),l=r.useRef(null),s=i?(l.current||(l.current=new window.Image),l.current):null,[d,f]=r.useState(()=>x(s,e));return(0,a.N)(()=>{f(x(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),r=e("error");return s.addEventListener("load",t),s.addEventListener("error",r),n&&(s.referrerPolicy=n),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",r)}},[s,o,n]),d}(o,f),v=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(l.sG.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=m(w,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.sG.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=w;var E=v,k=y,M=b},4738:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},4783:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5273:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},5453:(e,t,n)=>{"use strict";n.d(t,{v:()=>u});var r=n(2115);let o=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},i=e=>e?o(e):o,a=e=>e,l=e=>{let t=i(e),n=e=>(function(e,t=a){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?l(e):l},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(2115),o=n(5155);function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},6707:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},6786:(e,t,n)=>{"use strict";function r(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var r;let o=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(r=n.getItem(e))?r:null;return i instanceof Promise?i.then(o):o(i)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}n.d(t,{KU:()=>r,Zr:()=>i});let o=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>o(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>o(t)(e)}}},i=(e,t)=>(n,i,a)=>{let l,u={storage:r(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},c=!1,s=new Set,d=new Set,f=u.storage;if(!f)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${u.name}', the given storage is currently unavailable.`),n(...e)},i,a);let p=()=>{let e=u.partialize({...i()});return f.setItem(u.name,{state:e,version:u.version})},h=a.setState;a.setState=(e,t)=>{h(e,t),p()};let m=e((...e)=>{n(...e),p()},i,a);a.getInitialState=()=>m;let v=()=>{var e,t;if(!f)return;c=!1,s.forEach(e=>{var t;return e(null!=(t=i())?t:m)});let r=(null==(t=u.onRehydrateStorage)?void 0:t.call(u,null!=(e=i())?e:m))||void 0;return o(f.getItem.bind(f))(u.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===u.version)return[!1,e.state];else{if(u.migrate){let t=u.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[r,o]=e;if(n(l=u.merge(o,null!=(t=i())?t:m),!0),r)return p()}).then(()=>{null==r||r(l,void 0),l=i(),c=!0,d.forEach(e=>e(l))}).catch(e=>{null==r||r(void 0,e)})};return a.persist={setOptions:e=>{u={...u,...e},e.storage&&(f=e.storage)},clearStorage:()=>{null==f||f.removeItem(u.name)},getOptions:()=>u,rehydrate:()=>v(),hasHydrated:()=>c,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},u.skipHydration||v(),l||m}},7340:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7924:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8279:(e,t,n)=>{"use strict";n.d(t,{UC:()=>rF,q7:()=>rH,JU:()=>rW,ZL:()=>r_,bL:()=>rD,wv:()=>rz,l9:()=>rI});var r,o,i,a,l=n(2115),u=n.t(l,2);function c(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var s=n(6101),d=n(6081),f=n(2712),p=u[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return p(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var m=n(3655);function v(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function g(e,t){var n=v(e,t,"get");return n.get?n.get.call(e):n.value}function y(e,t,n){var r=v(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var w=n(9708),b=n(5155);function x(e){let t=e+"CollectionProvider",[n,r]=(0,d.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,b.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",c=(0,w.TL)(u),f=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),a=(0,s.s)(t,o.collectionRef);return(0,b.jsx)(c,{ref:a,children:r})});f.displayName=u;let p=e+"CollectionItemSlot",h="data-radix-collection-item",m=(0,w.TL)(p),v=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),u=(0,s.s)(t,a),c=i(p,n);return l.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,b.jsx)(m,{...{[h]:""},ref:u,children:r})});return v.displayName=p,[{Provider:a,Slot:f,ItemSlot:v},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var E=new WeakMap;function k(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=M(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function M(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap;var R=l.createContext(void 0);function A(e){let t=l.useContext(R);return e||t||"ltr"}var C=n(9033),S="dismissableLayer.update",T=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),N=l.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:d,onInteractOutside:f,onDismiss:p,...h}=e,v=l.useContext(T),[g,y]=l.useState(null),w=null!=(r=null==g?void 0:g.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=l.useState({}),E=(0,s.s)(t,e=>y(e)),k=Array.from(v.layers),[M]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),R=k.indexOf(M),A=g?k.indexOf(g):-1,N=v.layersWithOutsidePointerEventsDisabled.size>0,j=A>=R,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,C.c)(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){P("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));j&&!n&&(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},w),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,C.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&P("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},w);return!function(e,t=globalThis?.document){let n=(0,C.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A===v.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},w),l.useEffect(()=>{if(g)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(i=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(g)),v.layers.add(g),L(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=i)}},[g,w,o,v]),l.useEffect(()=>()=>{g&&(v.layers.delete(g),v.layersWithOutsidePointerEventsDisabled.delete(g),L())},[g,v]),l.useEffect(()=>{let e=()=>x({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,b.jsx)(m.sG.div,{...h,ref:E,style:{pointerEvents:N?j?"auto":"none":void 0,...e.style},onFocusCapture:c(e.onFocusCapture,D.onFocusCapture),onBlurCapture:c(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:c(e.onPointerDownCapture,O.onPointerDownCapture)})});function L(){let e=new CustomEvent(S);document.dispatchEvent(e)}function P(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,m.hO)(i,a):i.dispatchEvent(a)}N.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(T),r=l.useRef(null),o=(0,s.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,b.jsx)(m.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var j=0;function O(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var D="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},F=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,c]=l.useState(null),d=(0,C.c)(o),f=(0,C.c)(i),p=l.useRef(null),h=(0,s.s)(t,e=>c(e)),v=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(v.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:z(p.current,{select:!0})},t=function(e){if(v.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||z(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&z(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,v.paused]),l.useEffect(()=>{if(u){B.add(v);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(D,_);u.addEventListener(D,d),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(z(r,{select:t}),document.activeElement!==n)return}(W(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&z(u))}return()=>{u.removeEventListener(D,d),setTimeout(()=>{let t=new CustomEvent(I,_);u.addEventListener(I,f),u.dispatchEvent(t),t.defaultPrevented||z(null!=e?e:document.body,{select:!0}),u.removeEventListener(I,f),B.remove(v)},0)}}},[u,d,f,v]);let g=l.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=W(e);return[H(t,e),H(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&z(i,{select:!0})):(e.preventDefault(),n&&z(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,b.jsx)(m.sG.div,{tabIndex:-1,...a,ref:h,onKeyDown:g})});function W(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function H(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function z(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}F.displayName="FocusScope";var B=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=K(e,t)).unshift(t)},remove(t){var n;null==(n=(e=K(e,t))[0])||n.resume()}}}();function K(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var G=u[" useId ".trim().toString()]||(()=>void 0),U=0;function V(e){let[t,n]=l.useState(G());return(0,f.N)(()=>{e||n(e=>e??String(U++))},[e]),e||(t?`radix-${t}`:"")}let q=["top","right","bottom","left"],X=Math.min,Y=Math.max,$=Math.round,Z=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function et(e,t){return"function"==typeof e?e(t):e}function en(e){return e.split("-")[0]}function er(e){return e.split("-")[1]}function eo(e){return"x"===e?"y":"x"}function ei(e){return"y"===e?"height":"width"}function ea(e){return["top","bottom"].includes(en(e))?"y":"x"}function el(e){return e.replace(/start|end/g,e=>ee[e])}function eu(e){return e.replace(/left|right|bottom|top/g,e=>Q[e])}function ec(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function es(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ed(e,t,n){let r,{reference:o,floating:i}=e,a=ea(t),l=eo(ea(t)),u=ei(l),c=en(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(er(t)){case"start":r[l]-=p*(n&&s?-1:1);break;case"end":r[l]+=p*(n&&s?-1:1)}return r}let ef=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=ed(c,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=ed(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function ep(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=et(t,e),h=ec(p),m=l[f?"floating"===d?"reference":"floating":d],v=es(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=es(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function eh(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function em(e){return q.some(t=>e[t]>=0)}async function ev(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=en(n),l=er(n),u="y"===ea(n),c=["left","top"].includes(a)?-1:1,s=i&&u?-1:1,d=et(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function eg(){return"undefined"!=typeof window}function ey(e){return ex(e)?(e.nodeName||"").toLowerCase():"#document"}function ew(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eb(e){var t;return null==(t=(ex(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ex(e){return!!eg()&&(e instanceof Node||e instanceof ew(e).Node)}function eE(e){return!!eg()&&(e instanceof Element||e instanceof ew(e).Element)}function ek(e){return!!eg()&&(e instanceof HTMLElement||e instanceof ew(e).HTMLElement)}function eM(e){return!!eg()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ew(e).ShadowRoot)}function eR(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eN(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function eA(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eC(e){let t=eS(),n=eE(e)?eN(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function eS(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eT(e){return["html","body","#document"].includes(ey(e))}function eN(e){return ew(e).getComputedStyle(e)}function eL(e){return eE(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eP(e){if("html"===ey(e))return e;let t=e.assignedSlot||e.parentNode||eM(e)&&e.host||eb(e);return eM(t)?t.host:t}function ej(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eP(t);return eT(n)?t.ownerDocument?t.ownerDocument.body:t.body:ek(n)&&eR(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ew(o);if(i){let e=eO(a);return t.concat(a,a.visualViewport||[],eR(o)?o:[],e&&n?ej(e):[])}return t.concat(o,ej(o,[],n))}function eO(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eD(e){let t=eN(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ek(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=$(n)!==i||$(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eI(e){return eE(e)?e:e.contextElement}function e_(e){let t=eI(e);if(!ek(t))return J(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eD(t),a=(i?$(n.width):n.width)/r,l=(i?$(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eF=J(0);function eW(e){let t=ew(e);return eS()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eF}function eH(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eI(e),l=J(1);t&&(r?eE(r)&&(l=e_(r)):l=e_(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ew(a))&&o)?eW(a):J(0),c=(i.left+u.x)/l.x,s=(i.top+u.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ew(a),t=r&&eE(r)?ew(r):r,n=e,o=eO(n);for(;o&&r&&t!==n;){let e=e_(o),t=o.getBoundingClientRect(),r=eN(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=a,o=eO(n=ew(o))}}return es({width:d,height:f,x:c,y:s})}function ez(e,t){let n=eL(e).scrollLeft;return t?t.left+n:eH(eb(e)).left+n}function eB(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ez(e,r)),y:r.top+t.scrollTop}}function eK(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ew(e),r=eb(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=eS();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=eb(e),n=eL(e),r=e.ownerDocument.body,o=Y(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Y(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ez(e),l=-n.scrollTop;return"rtl"===eN(r).direction&&(a+=Y(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(eb(e));else if(eE(t))r=function(e,t){let n=eH(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ek(e)?e_(e):J(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eW(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return es(r)}function eG(e){return"static"===eN(e).position}function eU(e,t){if(!ek(e)||"fixed"===eN(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eb(e)===n&&(n=n.ownerDocument.body),n}function eV(e,t){let n=ew(e);if(eA(e))return n;if(!ek(e)){let t=eP(e);for(;t&&!eT(t);){if(eE(t)&&!eG(t))return t;t=eP(t)}return n}let r=eU(e,t);for(;r&&["table","td","th"].includes(ey(r))&&eG(r);)r=eU(r,t);return r&&eT(r)&&eG(r)&&!eC(r)?n:r||function(e){let t=eP(e);for(;ek(t)&&!eT(t);){if(eC(t))return t;if(eA(t))break;t=eP(t)}return null}(e)||n}let eq=async function(e){let t=this.getOffsetParent||eV,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ek(t),o=eb(t),i="fixed"===n,a=eH(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=J(0);if(r||!r&&!i)if(("body"!==ey(t)||eR(o))&&(l=eL(t)),r){let e=eH(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ez(o));i&&!r&&o&&(u.x=ez(o));let c=!o||r||i?J(0):eB(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eX={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=eb(r),l=!!t&&eA(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=J(1),s=J(0),d=ek(r);if((d||!d&&!i)&&(("body"!==ey(r)||eR(a))&&(u=eL(r)),ek(r))){let e=eH(r);c=e_(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||i?J(0):eB(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:eb,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eA(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ej(e,[],!1).filter(e=>eE(e)&&"body"!==ey(e)),o=null,i="fixed"===eN(e).position,a=i?eP(e):e;for(;eE(a)&&!eT(a);){let t=eN(a),n=eC(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eR(a)&&!n&&function e(t,n){let r=eP(t);return!(r===n||!eE(r)||eT(r))&&("fixed"===eN(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=eP(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eK(t,n,o);return e.top=Y(r.top,e.top),e.right=X(r.right,e.right),e.bottom=X(r.bottom,e.bottom),e.left=Y(r.left,e.left),e},eK(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eV,getElementRects:eq,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eD(e);return{width:t,height:n}},getScale:e_,isElement:eE,isRTL:function(e){return"rtl"===eN(e).direction}};function eY(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e$=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:c,padding:s=0}=et(e,t)||{};if(null==c)return{};let d=ec(s),f={x:n,y:r},p=eo(ea(o)),h=ei(p),m=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==a.isElement?void 0:a.isElement(b))||(x=l.floating[g]||i.floating[h]);let E=x/2-m[h]/2-1,k=X(d[v?"top":"left"],E),M=X(d[v?"bottom":"right"],E),R=x-m[h]-M,A=x/2-m[h]/2+(y/2-w/2),C=Y(k,X(A,R)),S=!u.arrow&&null!=er(o)&&A!==C&&i.reference[h]/2-(A<k?k:M)-m[h]/2<0,T=S?A<k?A-k:A-R:0;return{[p]:f[p]+T,data:{[p]:C,centerOffset:A-C-T,...S&&{alignmentOffset:T}},reset:S}}}),eZ=(e,t,n)=>{let r=new Map,o={platform:eX,...n},i={...o.platform,_c:r};return ef(e,t,{...o,platform:i})};var eJ=n(7650),eQ="undefined"!=typeof document?l.useLayoutEffect:function(){};function e0(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e0(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e0(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e1(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e2(e,t){let n=e1(e);return Math.round(t*n)/n}function e4(e){let t=l.useRef(e);return eQ(()=>{t.current=e}),t}let e3=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e$({element:n.current,padding:r}).fn(t):{}:n?e$({element:n,padding:r}).fn(t):{}}}),e9=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await ev(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),e6=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=et(e,t),c={x:n,y:r},s=await ep(t,u),d=ea(en(o)),f=eo(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=Y(n,X(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=Y(n,X(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),e7=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=et(e,t),s={x:n,y:r},d=ea(o),f=eo(d),p=s[f],h=s[d],m=et(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(en(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),e5=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=et(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=en(l),x=ea(s),E=en(s)===s,k=await (null==d.isRTL?void 0:d.isRTL(f.floating)),M=m||(E||!y?[eu(s)]:function(e){let t=eu(e);return[el(e),t,el(t)]}(s)),R="none"!==g;!m&&R&&M.push(...function(e,t,n,r){let o=er(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(en(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(el)))),i}(s,y,g,k));let A=[s,...M],C=await ep(t,w),S=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&S.push(C[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=er(e),o=eo(ea(e)),i=ei(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=eu(a)),[a,eu(a)]}(l,c,k);S.push(C[e[0]],C[e[1]])}if(T=[...T,{placement:l,overflows:S}],!S.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||x===ea(t)||T.every(e=>e.overflows[0]>0&&ea(e.placement)===x)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=T.filter(e=>{if(R){let t=ea(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:u,elements:c}=t,{apply:s=()=>{},...d}=et(e,t),f=await ep(t,d),p=en(a),h=er(a),m="y"===ea(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,b=X(g-f[o],y),x=X(v-f[i],w),E=!t.middlewareData.shift,k=b,M=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(M=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=y),E&&!h){let e=Y(f.left,0),t=Y(f.right,0),n=Y(f.top,0),r=Y(f.bottom,0);m?M=v-2*(0!==e||0!==t?e+t:Y(f.left,f.right)):k=g-2*(0!==n||0!==r?n+r:Y(f.top,f.bottom))}await s({...t,availableWidth:M,availableHeight:k});let R=await u.getDimensions(c.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=et(e,t);switch(r){case"referenceHidden":{let e=eh(await ep(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:em(e)}}}case"escaped":{let e=eh(await ep(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:em(e)}}}default:return{}}}}}(e),options:[e,t]}),tt=(e,t)=>({...e3(e),options:[e,t]});var tn=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,b.jsx)(m.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,b.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tn.displayName="Arrow";var tr="Popper",[to,ti]=(0,d.A)(tr),[ta,tl]=to(tr),tu=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,b.jsx)(ta,{scope:t,anchor:r,onAnchorChange:o,children:n})};tu.displayName=tr;var tc="PopperAnchor",ts=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tl(tc,n),a=l.useRef(null),u=(0,s.s)(t,a);return l.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,b.jsx)(m.sG.div,{...o,ref:u})});ts.displayName=tc;var td="PopperContent",[tf,tp]=to(td),th=l.forwardRef((e,t)=>{var n,r,o,i,a,u,c,d;let{__scopePopper:p,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:E=[],collisionPadding:k=0,sticky:M="partial",hideWhenDetached:R=!1,updatePositionStrategy:A="optimized",onPlaced:S,...T}=e,N=tl(td,p),[L,P]=l.useState(null),j=(0,s.s)(t,e=>P(e)),[O,D]=l.useState(null),I=function(e){let[t,n]=l.useState(void 0);return(0,f.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(O),_=null!=(c=null==I?void 0:I.width)?c:0,F=null!=(d=null==I?void 0:I.height)?d:0,W="number"==typeof k?k:{top:0,right:0,bottom:0,left:0,...k},H=Array.isArray(E)?E:[E],z=H.length>0,B={padding:W,boundary:H.filter(ty),altBoundary:z},{refs:K,floatingStyles:G,placement:U,isPositioned:V,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=l.useState(r);e0(p,r)||h(r);let[m,v]=l.useState(null),[g,y]=l.useState(null),w=l.useCallback(e=>{e!==k.current&&(k.current=e,v(e))},[]),b=l.useCallback(e=>{e!==M.current&&(M.current=e,y(e))},[]),x=i||m,E=a||g,k=l.useRef(null),M=l.useRef(null),R=l.useRef(d),A=null!=c,C=e4(c),S=e4(o),T=e4(s),N=l.useCallback(()=>{if(!k.current||!M.current)return;let e={placement:t,strategy:n,middleware:p};S.current&&(e.platform=S.current),eZ(k.current,M.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};L.current&&!e0(R.current,t)&&(R.current=t,eJ.flushSync(()=>{f(t)}))})},[p,t,n,S,T]);eQ(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let L=l.useRef(!1);eQ(()=>(L.current=!0,()=>{L.current=!1}),[]),eQ(()=>{if(x&&(k.current=x),E&&(M.current=E),x&&E){if(C.current)return C.current(x,E,N);N()}},[x,E,N,C,A]);let P=l.useMemo(()=>({reference:k,floating:M,setReference:w,setFloating:b}),[w,b]),j=l.useMemo(()=>({reference:x,floating:E}),[x,E]),O=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=e2(j.floating,d.x),r=e2(j.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...e1(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:N,refs:P,elements:j,floatingStyles:O}),[d,N,P,j,O])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eI(e),d=i||a?[...s?ej(s):[],...ej(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=eb(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let h=Z(d),m=Z(o.clientWidth-(s+f)),v={rootMargin:-h+"px "+-m+"px "+-Z(o.clientHeight-(d+p))+"px "+-Z(s)+"px",threshold:Y(0,X(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eY(c,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eH(e):null;return c&&function t(){let r=eH(e);m&&!eY(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:N.anchor},middleware:[e9({mainAxis:v+F,alignmentAxis:y}),x&&e6({mainAxis:!0,crossAxis:!1,limiter:"partial"===M?e7():void 0,...B}),x&&e5({...B}),e8({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),O&&tt({element:O,padding:w}),tw({arrowWidth:_,arrowHeight:F}),R&&te({strategy:"referenceHidden",...B})]}),[$,J]=tb(U),Q=(0,C.c)(S);(0,f.N)(()=>{V&&(null==Q||Q())},[V,Q]);let ee=null==(n=q.arrow)?void 0:n.x,et=null==(r=q.arrow)?void 0:r.y,en=(null==(o=q.arrow)?void 0:o.centerOffset)!==0,[er,eo]=l.useState();return(0,f.N)(()=>{L&&eo(window.getComputedStyle(L).zIndex)},[L]),(0,b.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:V?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(i=q.transformOrigin)?void 0:i.x,null==(a=q.transformOrigin)?void 0:a.y].join(" "),...(null==(u=q.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,b.jsx)(tf,{scope:p,placedSide:$,onArrowChange:D,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,b.jsx)(m.sG.div,{"data-side":$,"data-align":J,...T,ref:j,style:{...T.style,animation:V?void 0:"none"}})})})});th.displayName=td;var tm="PopperArrow",tv={top:"bottom",right:"left",bottom:"top",left:"right"},tg=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tp(tm,n),i=tv[o.placedSide];return(0,b.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,b.jsx)(tn,{...r,ref:t,style:{...r.style,display:"block"}})})});function ty(e){return null!==e}tg.displayName=tm;var tw=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=tb(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+f/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function tb(e){let[t,n="center"]=e.split("-");return[t,n]}var tx=l.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[a,u]=l.useState(!1);(0,f.N)(()=>u(!0),[]);let c=o||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?eJ.createPortal((0,b.jsx)(m.sG.div,{...i,ref:t}),c):null});tx.displayName="Portal";var tE=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=l.useState(),i=l.useRef(null),a=l.useRef(e),u=l.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return l.useEffect(()=>{let e=tk(i.current);u.current="mounted"===c?e:"none"},[c]),(0,f.N)(()=>{let t=i.current,n=a.current;if(n!==e){let r=u.current,o=tk(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),a.current=e}},[e,s]),(0,f.N)(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=tk(i.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=tk(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):l.Children.only(n),i=(0,s.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?l.cloneElement(o,{ref:i}):null};function tk(e){return(null==e?void 0:e.animationName)||"none"}tE.displayName="Presence";var tM="rovingFocusGroup.onEntryFocus",tR={bubbles:!1,cancelable:!0},tA="RovingFocusGroup",[tC,tS,tT]=x(tA),[tN,tL]=(0,d.A)(tA,[tT]),[tP,tj]=tN(tA),tO=l.forwardRef((e,t)=>(0,b.jsx)(tC.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,b.jsx)(tC.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,b.jsx)(tD,{...e,ref:t})})}));tO.displayName=tA;var tD=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:a,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:d,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...v}=e,g=l.useRef(null),y=(0,s.s)(t,g),w=A(i),[x,E]=h({prop:a,defaultProp:null!=u?u:null,onChange:d,caller:tA}),[k,M]=l.useState(!1),R=(0,C.c)(f),S=tS(n),T=l.useRef(!1),[N,L]=l.useState(0);return l.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(tM,R),()=>e.removeEventListener(tM,R)},[R]),(0,b.jsx)(tP,{scope:n,orientation:r,dir:w,loop:o,currentTabStopId:x,onItemFocus:l.useCallback(e=>E(e),[E]),onItemShiftTab:l.useCallback(()=>M(!0),[]),onFocusableItemAdd:l.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:l.useCallback(()=>L(e=>e-1),[]),children:(0,b.jsx)(m.sG.div,{tabIndex:k||0===N?-1:0,"data-orientation":r,...v,ref:y,style:{outline:"none",...e.style},onMouseDown:c(e.onMouseDown,()=>{T.current=!0}),onFocus:c(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!k){let t=new CustomEvent(tM,tR);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);tW([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),p)}}T.current=!1}),onBlur:c(e.onBlur,()=>M(!1))})})}),tI="RovingFocusGroupItem",t_=l.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:a,...u}=e,s=V(),d=i||s,f=tj(tI,n),p=f.currentTabStopId===d,h=tS(n),{onFocusableItemAdd:v,onFocusableItemRemove:g,currentTabStopId:y}=f;return l.useEffect(()=>{if(r)return v(),()=>g()},[r,v,g]),(0,b.jsx)(tC.ItemSlot,{scope:n,id:d,focusable:r,active:o,children:(0,b.jsx)(m.sG.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...u,ref:t,onMouseDown:c(e.onMouseDown,e=>{r?f.onItemFocus(d):e.preventDefault()}),onFocus:c(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:c(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tF[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tW(n))}}),children:"function"==typeof a?a({isCurrentTabStop:p,hasTabStop:null!=y}):a})})});t_.displayName=tI;var tF={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tW(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tH=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tz=new WeakMap,tB=new WeakMap,tK={},tG=0,tU=function(e){return e&&(e.host||tU(e.parentNode))},tV=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tU(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tK[n]||(tK[n]=new WeakMap);var i=tK[n],a=[],l=new Set,u=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tz.get(e)||0)+1,c=(i.get(e)||0)+1;tz.set(e,u),i.set(e,c),a.push(e),1===u&&o&&tB.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),l.clear(),tG++,function(){a.forEach(function(e){var t=tz.get(e)-1,o=i.get(e)-1;tz.set(e,t),i.set(e,o),t||(tB.has(e)||e.removeAttribute(r),tB.delete(e)),o||e.removeAttribute(n)}),--tG||(tz=new WeakMap,tz=new WeakMap,tB=new WeakMap,tK={})}},tq=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tH(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tV(r,o,n,"aria-hidden")):function(){return null}},tX=function(){return(tX=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tY(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var t$=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tZ="width-before-scroll-bar";function tJ(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tQ="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,t0=new WeakMap;function t1(e){return e}var t2=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=t1),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tX({async:!0,ssr:!1},e),i}(),t4=function(){},t3=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:t4,onWheelCapture:t4,onTouchMoveCapture:t4}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,k=e.gapMode,M=tY(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tJ(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tQ(function(){var e=t0.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tJ(e,null)}),r.forEach(function(e){t.has(e)||tJ(e,o)})}t0.set(i,n)},[n]),i),A=tX(tX({},M),c);return l.createElement(l.Fragment,null,m&&l.createElement(g,{sideCar:t2,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:b,setCallbacks:s,allowPinchZoom:!!x,lockRef:a,gapMode:k}),d?l.cloneElement(l.Children.only(f),tX(tX({},A),{ref:R})):l.createElement(void 0===E?"div":E,tX({},A,{className:p,ref:R}),f))});t3.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t3.classNames={fullWidth:tZ,zeroRight:t$};var t9=function(e){var t=e.sideCar,n=tY(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tX({},n))};t9.isSideCarExport=!0;var t6=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t7=function(){var e=t6();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t5=function(){var e=t7();return function(t){return e(t.styles,t.dynamic),null}},t8={left:0,top:0,right:0,gap:0},ne=function(e){return parseInt(e||"",10)||0},nt=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ne(n),ne(r),ne(o)]},nn=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t8;var t=nt(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},nr=t5(),no="data-scroll-locked",ni=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(no,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(t$," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tZ," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(t$," .").concat(t$," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(no,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},na=function(){var e=parseInt(document.body.getAttribute(no)||"0",10);return isFinite(e)?e:0},nl=function(){l.useEffect(function(){return document.body.setAttribute(no,(na()+1).toString()),function(){var e=na()-1;e<=0?document.body.removeAttribute(no):document.body.setAttribute(no,e.toString())}},[])},nu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;nl();var i=l.useMemo(function(){return nn(o)},[o]);return l.createElement(nr,{styles:ni(i,!t,o,n?"":"!important")})},nc=!1;if("undefined"!=typeof window)try{var ns=Object.defineProperty({},"passive",{get:function(){return nc=!0,!0}});window.addEventListener("test",ns,ns),window.removeEventListener("test",ns,ns)}catch(e){nc=!1}var nd=!!nc&&{passive:!1},nf=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},np=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nh(e,r)){var o=nm(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nh=function(e,t){return"v"===e?nf(t,"overflowY"):nf(t,"overflowX")},nm=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nv=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=nm(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&nh(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},ng=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ny=function(e){return[e.deltaX,e.deltaY]},nw=function(e){return e&&"current"in e?e.current:e},nb=0,nx=[];let nE=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(nb++)[0],i=l.useState(t5)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nw),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=ng(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=np(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=np(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nv(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(nx.length&&nx[nx.length-1]===i){var n="deltaY"in e?ny(e):ng(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nw).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=ng(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,ny(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,ng(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return nx.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,nd),document.addEventListener("touchmove",c,nd),document.addEventListener("touchstart",d,nd),function(){nx=nx.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,nd),document.removeEventListener("touchmove",c,nd),document.removeEventListener("touchstart",d,nd)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(nu,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t2.useMedium(r),t9);var nk=l.forwardRef(function(e,t){return l.createElement(t3,tX({},e,{ref:t,sideCar:nE}))});nk.classNames=t3.classNames;var nM=["Enter"," "],nR=["ArrowUp","PageDown","End"],nA=["ArrowDown","PageUp","Home",...nR],nC={ltr:[...nM,"ArrowRight"],rtl:[...nM,"ArrowLeft"]},nS={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nT="Menu",[nN,nL,nP]=x(nT),[nj,nO]=(0,d.A)(nT,[nP,ti,tL]),nD=ti(),nI=tL(),[n_,nF]=nj(nT),[nW,nH]=nj(nT),nz=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:a=!0}=e,u=nD(t),[c,s]=l.useState(null),d=l.useRef(!1),f=(0,C.c)(i),p=A(o);return l.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,b.jsx)(tu,{...u,children:(0,b.jsx)(n_,{scope:t,open:n,onOpenChange:f,content:c,onContentChange:s,children:(0,b.jsx)(nW,{scope:t,onClose:l.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:a,children:r})})})};nz.displayName=nT;var nB=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nD(n);return(0,b.jsx)(ts,{...o,...r,ref:t})});nB.displayName="MenuAnchor";var nK="MenuPortal",[nG,nU]=nj(nK,{forceMount:void 0}),nV=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nF(nK,t);return(0,b.jsx)(nG,{scope:t,forceMount:n,children:(0,b.jsx)(tE,{present:n||i.open,children:(0,b.jsx)(tx,{asChild:!0,container:o,children:r})})})};nV.displayName=nK;var nq="MenuContent",[nX,nY]=nj(nq),n$=l.forwardRef((e,t)=>{let n=nU(nq,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nF(nq,e.__scopeMenu),a=nH(nq,e.__scopeMenu);return(0,b.jsx)(nN.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(tE,{present:r||i.open,children:(0,b.jsx)(nN.Slot,{scope:e.__scopeMenu,children:a.modal?(0,b.jsx)(nZ,{...o,ref:t}):(0,b.jsx)(nJ,{...o,ref:t})})})})}),nZ=l.forwardRef((e,t)=>{let n=nF(nq,e.__scopeMenu),r=l.useRef(null),o=(0,s.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return tq(e)},[]),(0,b.jsx)(n0,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:c(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),nJ=l.forwardRef((e,t)=>{let n=nF(nq,e.__scopeMenu);return(0,b.jsx)(n0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nQ=(0,w.TL)("MenuContent.ScrollLock"),n0=l.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:u,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:m,onDismiss:v,disableOutsideScroll:g,...y}=e,w=nF(nq,n),x=nH(nq,n),E=nD(n),k=nI(n),M=nL(n),[R,A]=l.useState(null),C=l.useRef(null),S=(0,s.s)(t,C,w.onContentChange),T=l.useRef(0),L=l.useRef(""),P=l.useRef(0),D=l.useRef(null),I=l.useRef("right"),_=l.useRef(0),W=g?nk:l.Fragment,H=e=>{var t,n;let r=L.current+e,o=M().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){L.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};l.useEffect(()=>()=>window.clearTimeout(T.current),[]),l.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:O()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:O()),j++,()=>{1===j&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),j--}},[]);let z=l.useCallback(e=>{var t,n;return I.current===(null==(t=D.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=D.current)?void 0:n.area)},[]);return(0,b.jsx)(nX,{scope:n,searchRef:L,onItemEnter:l.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:l.useCallback(e=>{var t;z(e)||(null==(t=C.current)||t.focus(),A(null))},[z]),onTriggerLeave:l.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:P,onPointerGraceIntentChange:l.useCallback(e=>{D.current=e},[]),children:(0,b.jsx)(W,{...g?{as:nQ,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(F,{asChild:!0,trapped:o,onMountAutoFocus:c(i,e=>{var t;e.preventDefault(),null==(t=C.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:(0,b.jsx)(N,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:m,onDismiss:v,children:(0,b.jsx)(tO,{asChild:!0,...k,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:R,onCurrentTabStopIdChange:A,onEntryFocus:c(d,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(th,{role:"menu","aria-orientation":"vertical","data-state":rv(w.open),"data-radix-menu-content":"",dir:x.dir,...E,...y,ref:S,style:{outline:"none",...y.style},onKeyDown:c(y.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&H(e.key));let o=C.current;if(e.target!==o||!nA.includes(e.key))return;e.preventDefault();let i=M().filter(e=>!e.disabled).map(e=>e.ref.current);nR.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:c(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),L.current="")}),onPointerMove:c(e.onPointerMove,rw(e=>{let t=e.target,n=_.current!==e.clientX;e.currentTarget.contains(t)&&n&&(I.current=e.clientX>_.current?"right":"left",_.current=e.clientX)}))})})})})})})});n$.displayName=nq;var n1=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(m.sG.div,{role:"group",...r,ref:t})});n1.displayName="MenuGroup";var n2=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(m.sG.div,{...r,ref:t})});n2.displayName="MenuLabel";var n4="MenuItem",n3="menu.itemSelect",n9=l.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=l.useRef(null),a=nH(n4,e.__scopeMenu),u=nY(n4,e.__scopeMenu),d=(0,s.s)(t,i),f=l.useRef(!1);return(0,b.jsx)(n6,{...o,ref:d,disabled:n,onClick:c(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(n3,{bubbles:!0,cancelable:!0});e.addEventListener(n3,e=>null==r?void 0:r(e),{once:!0}),(0,m.hO)(e,t),t.defaultPrevented?f.current=!1:a.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),f.current=!0},onPointerUp:c(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:c(e.onKeyDown,e=>{let t=""!==u.searchRef.current;n||t&&" "===e.key||nM.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});n9.displayName=n4;var n6=l.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,a=nY(n4,n),u=nI(n),d=l.useRef(null),f=(0,s.s)(t,d),[p,h]=l.useState(!1),[v,g]=l.useState("");return l.useEffect(()=>{let e=d.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,b.jsx)(nN.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:v,children:(0,b.jsx)(t_,{asChild:!0,...u,focusable:!r,children:(0,b.jsx)(m.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:f,onPointerMove:c(e.onPointerMove,rw(e=>{r?a.onItemLeave(e):(a.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:c(e.onPointerLeave,rw(e=>a.onItemLeave(e))),onFocus:c(e.onFocus,()=>h(!0)),onBlur:c(e.onBlur,()=>h(!1))})})})}),n7=l.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,b.jsx)(ri,{scope:e.__scopeMenu,checked:n,children:(0,b.jsx)(n9,{role:"menuitemcheckbox","aria-checked":rg(n)?"mixed":n,...o,ref:t,"data-state":ry(n),onSelect:c(o.onSelect,()=>null==r?void 0:r(!!rg(n)||!n),{checkForDefaultPrevented:!1})})})});n7.displayName="MenuCheckboxItem";var n5="MenuRadioGroup",[n8,re]=nj(n5,{value:void 0,onValueChange:()=>{}}),rt=l.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,C.c)(r);return(0,b.jsx)(n8,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,b.jsx)(n1,{...o,ref:t})})});rt.displayName=n5;var rn="MenuRadioItem",rr=l.forwardRef((e,t)=>{let{value:n,...r}=e,o=re(rn,e.__scopeMenu),i=n===o.value;return(0,b.jsx)(ri,{scope:e.__scopeMenu,checked:i,children:(0,b.jsx)(n9,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":ry(i),onSelect:c(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});rr.displayName=rn;var ro="MenuItemIndicator",[ri,ra]=nj(ro,{checked:!1}),rl=l.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=ra(ro,n);return(0,b.jsx)(tE,{present:r||rg(i.checked)||!0===i.checked,children:(0,b.jsx)(m.sG.span,{...o,ref:t,"data-state":ry(i.checked)})})});rl.displayName=ro;var ru=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,b.jsx)(m.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ru.displayName="MenuSeparator";var rc=l.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nD(n);return(0,b.jsx)(tg,{...o,...r,ref:t})});rc.displayName="MenuArrow";var[rs,rd]=nj("MenuSub"),rf="MenuSubTrigger",rp=l.forwardRef((e,t)=>{let n=nF(rf,e.__scopeMenu),r=nH(rf,e.__scopeMenu),o=rd(rf,e.__scopeMenu),i=nY(rf,e.__scopeMenu),a=l.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=l.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return l.useEffect(()=>p,[p]),l.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),d(null)}},[u,d]),(0,b.jsx)(nB,{asChild:!0,...f,children:(0,b.jsx)(n6,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":rv(n.open),...e,ref:(0,s.t)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:c(e.onPointerMove,rw(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||a.current||(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:c(e.onPointerLeave,rw(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,l=o[a?"left":"right"],c=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:c,y:o.top},{x:c,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:c(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&nC[r.dir].includes(t.key)){var a;n.onOpenChange(!0),null==(a=n.content)||a.focus(),t.preventDefault()}})})})});rp.displayName=rf;var rh="MenuSubContent",rm=l.forwardRef((e,t)=>{let n=nU(nq,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nF(nq,e.__scopeMenu),a=nH(nq,e.__scopeMenu),u=rd(rh,e.__scopeMenu),d=l.useRef(null),f=(0,s.s)(t,d);return(0,b.jsx)(nN.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(tE,{present:r||i.open,children:(0,b.jsx)(nN.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(n0,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:f,align:"start",side:"rtl"===a.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;a.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:c(e.onFocusOutside,e=>{e.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:c(e.onEscapeKeyDown,e=>{a.onClose(),e.preventDefault()}),onKeyDown:c(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nS[a.dir].includes(e.key);if(t&&n){var r;i.onOpenChange(!1),null==(r=u.trigger)||r.focus(),e.preventDefault()}})})})})})});function rv(e){return e?"open":"closed"}function rg(e){return"indeterminate"===e}function ry(e){return rg(e)?"indeterminate":e?"checked":"unchecked"}function rw(e){return t=>"mouse"===t.pointerType?e(t):void 0}rm.displayName=rh;var rb="DropdownMenu",[rx,rE]=(0,d.A)(rb,[nO]),rk=nO(),[rM,rR]=rx(rb),rA=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=rk(t),s=l.useRef(null),[d,f]=h({prop:o,defaultProp:null!=i&&i,onChange:a,caller:rb});return(0,b.jsx)(rM,{scope:t,triggerId:V(),triggerRef:s,contentId:V(),open:d,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,b.jsx)(nz,{...c,open:d,onOpenChange:f,dir:r,modal:u,children:n})})};rA.displayName=rb;var rC="DropdownMenuTrigger",rS=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=rR(rC,n),a=rk(n);return(0,b.jsx)(nB,{asChild:!0,...a,children:(0,b.jsx)(m.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,s.t)(t,i.triggerRef),onPointerDown:c(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:c(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rS.displayName=rC;var rT=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rk(t);return(0,b.jsx)(nV,{...r,...n})};rT.displayName="DropdownMenuPortal";var rN="DropdownMenuContent",rL=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rR(rN,n),i=rk(n),a=l.useRef(!1);return(0,b.jsx)(n$,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:c(e.onCloseAutoFocus,e=>{var t;a.current||null==(t=o.triggerRef.current)||t.focus(),a.current=!1,e.preventDefault()}),onInteractOutside:c(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rL.displayName=rN,l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(n1,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var rP=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(n2,{...o,...r,ref:t})});rP.displayName="DropdownMenuLabel";var rj=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(n9,{...o,...r,ref:t})});rj.displayName="DropdownMenuItem",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(n7,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rt,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rr,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rl,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var rO=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(ru,{...o,...r,ref:t})});rO.displayName="DropdownMenuSeparator",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rc,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rp,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rk(n);return(0,b.jsx)(rm,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var rD=rA,rI=rS,r_=rT,rF=rL,rW=rP,rH=rj,rz=rO},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}}]);