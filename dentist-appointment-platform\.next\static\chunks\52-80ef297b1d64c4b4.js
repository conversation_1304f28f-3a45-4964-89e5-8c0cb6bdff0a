(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[52],{375:(e,t,r)=>{var o=r(7476);function n(){var t,r,a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",i=a.toStringTag||"@@toStringTag";function l(e,n,a,s){var i=Object.create((n&&n.prototype instanceof u?n:u).prototype);return o(i,"_invoke",function(e,o,n){var a,s,i,l=0,u=n||[],d=!1,f={p:0,n:0,v:t,a:p,f:p.bind(t,4),d:function(e,r){return a=e,s=0,i=t,f.n=r,c}};function p(e,o){for(s=e,i=o,r=0;!d&&l&&!n&&r<u.length;r++){var n,a=u[r],p=f.p,m=a[2];e>3?(n=m===o)&&(i=a[(s=a[4])?5:(s=3,3)],a[4]=a[5]=t):a[0]<=p&&((n=e<2&&p<a[1])?(s=0,f.v=o,f.n=a[1]):p<m&&(n=e<3||a[0]>o||o>m)&&(a[4]=e,a[5]=o,f.n=m,s=0))}if(n||e>1)return c;throw d=!0,o}return function(n,u,m){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&p(u,m),s=u,i=m;(r=s<2?t:i)||!d;){a||(s?s<3?(s>1&&(f.n=-1),p(s,i)):f.n=i:f.v=i);try{if(l=2,a){if(s||(n="next"),r=a[n]){if(!(r=r.call(a,i)))throw TypeError("iterator result is not an object");if(!r.done)return r;i=r.value,s<2&&(s=0)}else 1===s&&(r=a.return)&&r.call(a),s<2&&(i=TypeError("The iterator does not provide a '"+n+"' method"),s=1);a=t}else if((r=(d=f.n<0)?i:e.call(o,f))!==c)break}catch(e){a=t,s=1,i=e}finally{l=1}}return{value:r,done:d}}}(e,a,s),!0),i}var c={};function u(){}function d(){}function f(){}r=Object.getPrototypeOf;var p=f.prototype=u.prototype=Object.create([][s]?r(r([][s]())):(o(r={},s,function(){return this}),r));function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,o(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return d.prototype=f,o(p,"constructor",f),o(f,"constructor",d),d.displayName="GeneratorFunction",o(f,i,"GeneratorFunction"),o(p),o(p,i,"Generator"),o(p,s,function(){return this}),o(p,"toString",function(){return"[object Generator]"}),(e.exports=n=function(){return{w:l,m:m}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},837:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let o=new URL(null!=(t=e)?t:r),n=("/"===o.pathname?r.pathname:o.pathname).replace(/\/$/,""),a=`${o.origin}${n}`;return{origin:o.origin,host:o.host,path:n,base:a,toString:()=>a}}},913:e=>{function t(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e.exports=t=function(){return!!r},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},1390:(e,t,r)=>{var o=r(2977)();e.exports=o;try{regeneratorRuntime=o}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}},1727:e=>{e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,s,i=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=a.call(r)).done)&&(i.push(o.value),i.length!==t);l=!0);}catch(e){c=!0,n=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw n}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},1883:(e,t,r)=>{"use strict";var o=r(6620);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,o){return r[o]=(0,a.default)(n.default.mark(function r(){var a,s,i,l,c,u=arguments;return n.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,s=Array(a=u.length),i=0;i<a;i++)s[i]=u[i];return t.debug("adapter_".concat(o),{args:s}),l=e[o],r.next=6,l.apply(void 0,s);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(o),r.t0),(c=new m(r.t0)).name="".concat(h(o),"Error"),c;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=h,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,o){return r[o]=(0,a.default)(n.default.mark(function r(){var a,s=arguments;return n.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,a=e[o],r.next=4,a.apply(void 0,s);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(b(o),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=b;var n=o(r(1390)),a=o(r(9165)),s=o(r(3901)),i=o(r(2419)),l=o(r(6045)),c=o(r(4170)),u=o(r(7824)),d=o(r(8683));function f(e,t,r){return t=(0,u.default)(t),(0,c.default)(e,p()?Reflect.construct(t,r||[],(0,u.default)(e).constructor):t.apply(e,r))}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(p=function(){return!!e})()}var m=t.UnknownError=function(e){function t(e){var r,o;return(0,i.default)(this,t),(o=f(this,t,[null!=(r=null==e?void 0:e.message)?r:e])).name="UnknownError",o.code=e.code,e instanceof Error&&(o.stack=e.stack),o}return(0,d.default)(t,e),(0,l.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,o(r(3131)).default)(Error));function b(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function h(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","OAuthCallbackError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.AccountNotLinkedError=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","AccountNotLinkedError"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAPIRoute=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","MissingAPIRouteError"),(0,s.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingSecret=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","MissingSecretError"),(0,s.default)(e,"code","NO_SECRET"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAuthorize=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","MissingAuthorizeError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAdapter=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","MissingAdapterError"),(0,s.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.MissingAdapterMethods=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","MissingAdapterMethodsError"),(0,s.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.UnsupportedStrategy=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","UnsupportedStrategyError"),(0,s.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m),t.InvalidCallbackUrl=function(e){function t(){var e;(0,i.default)(this,t);for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];return e=f(this,t,[].concat(o)),(0,s.default)(e,"name","InvalidCallbackUrl"),(0,s.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,d.default)(t,e),(0,l.default)(t)}(m)},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var o=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=o.$,s=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=t,l=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],o=null==i?void 0:i[e];if(null===t)return null;let a=n(t)||n(o);return s[e][a]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return a(e,l,null==t||null==(o=t.compoundVariants)?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2108:(e,t,r)=>{"use strict";var o,n,a,s,i,l=r(9509),c=r(6620),u=r(7382);Object.defineProperty(t,"__esModule",{value:!0});var d={SessionContext:!0,useSession:!0,getSession:!0,getCsrfToken:!0,getProviders:!0,signIn:!0,signOut:!0,SessionProvider:!0};t.SessionContext=void 0,t.SessionProvider=function(e){if(!M)throw Error("React Context is unavailable in Server Components");var t,r,o,n,a,s,i=e.children,l=e.basePath,c=e.refetchInterval,u=e.refetchWhenOffline;l&&(E.basePath=l);var d=void 0!==e.session;E._lastSync=d?(0,x.now)():0;var p=h.useState(function(){return d&&(E._session=e.session),e.session}),g=(0,b.default)(p,2),v=g[0],w=g[1],k=h.useState(!d),_=(0,b.default)(k,2),O=_[0],j=_[1];h.useEffect(function(){return E._getSession=(0,m.default)(f.default.mark(function e(){var t,r,o=arguments;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(o.length>0&&void 0!==o[0]?o[0]:{}).event,e.prev=1,!((r="storage"===t)||void 0===E._session)){e.next=10;break}return E._lastSync=(0,x.now)(),e.next=7,R({broadcast:!r});case 7:return E._session=e.sent,w(E._session),e.abrupt("return");case 10:if(!(!t||null===E._session||(0,x.now)()<E._lastSync)){e.next=12;break}return e.abrupt("return");case 12:return E._lastSync=(0,x.now)(),e.next=15,R();case 15:E._session=e.sent,w(E._session),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(1),P.error("CLIENT_SESSION_ERROR",e.t0);case 22:return e.prev=22,j(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[1,19,22,25]])})),E._getSession(),function(){E._lastSync=0,E._session=void 0,E._getSession=function(){}}},[]),h.useEffect(function(){var e=S.receive(function(){return E._getSession({event:"storage"})});return function(){return e()}},[]),h.useEffect(function(){var t=e.refetchOnWindowFocus,r=void 0===t||t,o=function(){r&&"visible"===document.visibilityState&&E._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",o,!1),function(){return document.removeEventListener("visibilitychange",o,!1)}},[e.refetchOnWindowFocus]);var A=(t=h.useState("undefined"!=typeof navigator&&navigator.onLine),o=(r=(0,b.default)(t,2))[0],n=r[1],a=function(){return n(!0)},s=function(){return n(!1)},h.useEffect(function(){return window.addEventListener("online",a),window.addEventListener("offline",s),function(){window.removeEventListener("online",a),window.removeEventListener("offline",s)}},[]),o),N=!1!==u||A;h.useEffect(function(){if(c&&N){var e=setInterval(function(){E._session&&E._getSession({event:"poll"})},1e3*c);return function(){return clearInterval(e)}}},[c,N]);var z=h.useMemo(function(){return{data:v,status:O?"loading":v?"authenticated":"unauthenticated",update:function(e){return(0,m.default)(f.default.mark(function t(){var r;return f.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(O||!v)){t.next=2;break}return t.abrupt("return");case 2:return j(!0),t.t0=x.fetchData,t.t1=E,t.t2=P,t.next=8,C();case 8:return t.t3=t.sent,t.t4=e,t.t5={csrfToken:t.t3,data:t.t4},t.t6={body:t.t5},t.t7={req:t.t6},t.next=15,(0,t.t0)("session",t.t1,t.t2,t.t7);case 15:return r=t.sent,j(!1),r&&(w(r),S.post({event:"session",data:{trigger:"getSession"}})),t.abrupt("return",r);case 19:case"end":return t.stop()}},t)}))()}}},[v,O]);return(0,y.jsx)(M.Provider,{value:z,children:i})},t.getCsrfToken=C,t.getProviders=z,t.getSession=R,t.signIn=function(e,t,r){return L.apply(this,arguments)},t.signOut=function(e){return U.apply(this,arguments)},t.useSession=function(e){if(!M)throw Error("React Context is unavailable in Server Components");var t=h.useContext(M),r=null!=e?e:{},o=r.required,n=r.onUnauthenticated,a=o&&"unauthenticated"===t.status;return(h.useEffect(function(){if(a){var e="/api/auth/signin?".concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));n?n():window.location.href=e}},[a,n]),a)?{data:t.data,update:t.update,status:"loading"}:t};var f=c(r(1390)),p=c(r(3901)),m=c(r(9165)),b=c(r(5593)),h=_(r(2115)),g=_(r(5146)),v=c(r(837)),x=r(2630),y=r(5155),w=r(2477);function k(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(k=function(e){return e?r:t})(e)}function _(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=u(e)&&"function"!=typeof e)return{default:e};var r=k(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=n?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,r&&r.set(e,o),o}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){(0,p.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.keys(w).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(d,e))&&(e in t&&t[e]===w[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return w[e]}}))});var E={baseUrl:(0,v.default)(null!=(o=l.env.NEXTAUTH_URL)?o:l.env.VERCEL_URL).origin,basePath:(0,v.default)(l.env.NEXTAUTH_URL).path,baseUrlServer:(0,v.default)(null!=(n=null!=(a=l.env.NEXTAUTH_URL_INTERNAL)?a:l.env.NEXTAUTH_URL)?n:l.env.VERCEL_URL).origin,basePathServer:(0,v.default)(null!=(s=l.env.NEXTAUTH_URL_INTERNAL)?s:l.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:function(){}},S=(0,x.BroadcastChannel)(),P=(0,g.proxyLogger)(g.default,E.basePath),M=t.SessionContext=null==(i=h.createContext)?void 0:i.call(h,void 0);function R(e){return A.apply(this,arguments)}function A(){return(A=(0,m.default)(f.default.mark(function e(t){var r,o;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,x.fetchData)("session",E,P,t);case 2:return o=e.sent,(null==(r=null==t?void 0:t.broadcast)||r)&&S.post({event:"session",data:{trigger:"getSession"}}),e.abrupt("return",o);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function C(e){return N.apply(this,arguments)}function N(){return(N=(0,m.default)(f.default.mark(function e(t){var r;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,x.fetchData)("csrf",E,P,t);case 2:return r=e.sent,e.abrupt("return",null==r?void 0:r.csrfToken);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function z(){return T.apply(this,arguments)}function T(){return(T=(0,m.default)(f.default.mark(function e(){return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,x.fetchData)("providers",E,P);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function L(){return(L=(0,m.default)(f.default.mark(function e(t,r,o){var n,a,s,i,l,c,u,d,p,m,b,h,g,v,y,w,k;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=void 0===(a=(n=null!=r?r:{}).callbackUrl)?window.location.href:a,l=void 0===(i=n.redirect)||i,c=(0,x.apiBaseUrl)(E),e.next=4,z();case 4:if(u=e.sent){e.next=8;break}return window.location.href="".concat(c,"/error"),e.abrupt("return");case 8:if(!(!t||!(t in u))){e.next=11;break}return window.location.href="".concat(c,"/signin?").concat(new URLSearchParams({callbackUrl:s})),e.abrupt("return");case 11:return d="credentials"===u[t].type,p="email"===u[t].type,m=d||p,b="".concat(c,"/").concat(d?"callback":"signin","/").concat(t),h="".concat(b).concat(o?"?".concat(new URLSearchParams(o)):""),e.t0=fetch,e.t1=h,e.t2={"Content-Type":"application/x-www-form-urlencoded"},e.t3=URLSearchParams,e.t4=j,e.t5=j({},r),e.t6={},e.next=25,C();case 25:return e.t7=e.sent,e.t8=s,e.t9={csrfToken:e.t7,callbackUrl:e.t8,json:!0},e.t10=(0,e.t4)(e.t5,e.t6,e.t9),e.t11=new e.t3(e.t10),e.t12={method:"post",headers:e.t2,body:e.t11},e.next=33,(0,e.t0)(e.t1,e.t12);case 33:return g=e.sent,e.next=36,g.json();case 36:if(v=e.sent,!(l||!m)){e.next=42;break}return w=null!=(y=v.url)?y:s,window.location.href=w,w.includes("#")&&window.location.reload(),e.abrupt("return");case 42:if(k=new URL(v.url).searchParams.get("error"),!g.ok){e.next=46;break}return e.next=46,E._getSession({event:"storage"});case 46:return e.abrupt("return",{error:k,status:g.status,ok:g.ok,url:k?null:v.url});case 47:case"end":return e.stop()}},e)}))).apply(this,arguments)}function U(){return(U=(0,m.default)(f.default.mark(function e(t){var r,o,n,a,s,i,l,c,u;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=void 0===(o=(null!=t?t:{}).callbackUrl)?window.location.href:o,a=(0,x.apiBaseUrl)(E),e.t0={"Content-Type":"application/x-www-form-urlencoded"},e.t1=URLSearchParams,e.next=6,C();case 6:return e.t2=e.sent,e.t3=n,e.t4={csrfToken:e.t2,callbackUrl:e.t3,json:!0},e.t5=new e.t1(e.t4),s={method:"post",headers:e.t0,body:e.t5},e.next=13,fetch("".concat(a,"/signout"),s);case 13:return i=e.sent,e.next=16,i.json();case 16:if(l=e.sent,S.post({event:"session",data:{trigger:"signout"}}),!(null==(r=null==t?void 0:t.redirect)||r)){e.next=23;break}return u=null!=(c=l.url)?c:n,window.location.href=u,u.includes("#")&&window.location.reload(),e.abrupt("return");case 23:return e.next=25,E._getSession({event:"storage"});case 25:return e.abrupt("return",l);case 26:case"end":return e.stop()}},e)}))).apply(this,arguments)}},2402:e=>{e.exports=function(e,t){this.v=e,this.k=t},e.exports.__esModule=!0,e.exports.default=e.exports},2419:e=>{e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},2477:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},2596:(e,t,r)=>{"use strict";function o(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=t);return o}r.d(t,{$:()=>o})},2630:(e,t,r)=>{"use strict";var o=r(6620);Object.defineProperty(t,"__esModule",{value:!0}),t.BroadcastChannel=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"nextauth.message";return{receive:function(t){var r=function(r){if(r.key===e){var o,n=JSON.parse(null!=(o=r.newValue)?o:"{}");(null==n?void 0:n.event)==="session"&&null!=n&&n.data&&t(n)}};return window.addEventListener("storage",r),function(){return window.removeEventListener("storage",r)}},post:function(t){if("undefined"!=typeof window)try{localStorage.setItem(e,JSON.stringify(l(l({},t),{},{timestamp:d()})))}catch(e){}}}},t.apiBaseUrl=u,t.fetchData=function(e,t,r){return c.apply(this,arguments)},t.now=d;var n=o(r(1390)),a=o(r(3901)),s=o(r(9165));function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(){return(c=(0,s.default)(n.default.mark(function e(t,r,o){var a,s,i,c,d,f,p,m,b,h=arguments;return n.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s=(a=h.length>3&&void 0!==h[3]?h[3]:{}).ctx,c=void 0===(i=a.req)?null==s?void 0:s.req:i,d="".concat(u(r),"/").concat(t),e.prev=2,p={headers:l({"Content-Type":"application/json"},null!=c&&null!=(f=c.headers)&&f.cookie?{cookie:c.headers.cookie}:{})},null!=c&&c.body&&(p.body=JSON.stringify(c.body),p.method="POST"),e.next=7,fetch(d,p);case 7:return m=e.sent,e.next=10,m.json();case 10:if(b=e.sent,m.ok){e.next=13;break}throw b;case 13:return e.abrupt("return",Object.keys(b).length>0?b:null);case 16:return e.prev=16,e.t0=e.catch(2),o.error("CLIENT_FETCH_ERROR",{error:e.t0,url:d}),e.abrupt("return",null);case 20:case"end":return e.stop()}},e,null,[[2,16]])}))).apply(this,arguments)}function u(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function d(){return Math.floor(Date.now()/1e3)}},2977:(e,t,r)=>{var o=r(2402),n=r(375),a=r(7149),s=r(8977),i=r(5591),l=r(8079),c=r(4801);function u(){"use strict";var t=n(),r=t.m(u),d=(Object.getPrototypeOf?Object.getPrototypeOf(r):r.__proto__).constructor;function f(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))}var p={throw:1,return:2,break:3,continue:3};function m(e){var t,r;return function(o){t||(t={stop:function(){return r(o.a,2)},catch:function(){return o.v},abrupt:function(e,t){return r(o.a,p[e],t)},delegateYield:function(e,n,a){return t.resultName=n,r(o.d,c(e),a)},finish:function(e){return r(o.f,e)}},r=function(e,r,n){o.p=t.prev,o.n=t.next;try{return e(r,n)}finally{t.next=o.n}}),t.resultName&&(t[t.resultName]=o.v,t.resultName=void 0),t.sent=o.v,t.next=o.n;try{return e.call(this,t)}finally{o.p=t.prev,o.n=t.next}}}return(e.exports=u=function(){return{wrap:function(e,r,o,n){return t.w(m(e),r,o,n&&n.reverse())},isGeneratorFunction:f,mark:t.m,awrap:function(e,t){return new o(e,t)},AsyncIterator:i,async:function(e,t,r,o,n){return(f(t)?s:a)(m(e),t,r,o,n)},keys:l,values:c}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=u,e.exports.__esModule=!0,e.exports.default=e.exports},3131:(e,t,r)=>{var o=r(7824),n=r(9828),a=r(5440),s=r(6268);function i(t){var r="function"==typeof Map?new Map:void 0;return e.exports=i=function(e){if(null===e||!a(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return s(e,arguments,o(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),n(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,i(t)}e.exports=i,e.exports.__esModule=!0,e.exports.default=e.exports},3539:e=>{e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o},e.exports.__esModule=!0,e.exports.default=e.exports},3901:(e,t,r)=>{var o=r(6346);e.exports=function(e,t,r){return(t=o(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},4170:(e,t,r)=>{var o=r(7382).default,n=r(8109);e.exports=function(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},4514:(e,t,r)=>{var o=r(3539);e.exports=function(e,t){if(e){if("string"==typeof e)return o(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},4801:(e,t,r)=>{var o=r(7382).default;e.exports=function(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],r=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}}}throw TypeError(o(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports},4947:e=>{e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},5146:(e,t,r)=>{"use strict";var o=r(6620);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},o=function(e){var o;r[e]=(o=(0,s.default)(n.default.mark(function r(o,s){var i,d;return n.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(u[e](o,s),"error"===e&&(s=c(s)),s.client=!0,i="".concat(t,"/_log"),d=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:o},s)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(i,d));case 8:return r.next=10,fetch(i,{method:"POST",body:d,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return o.apply(this,arguments)})};for(var i in e)o(i);return r}catch(e){return u}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(u.debug=function(){}),e.error&&(u.error=e.error),e.warn&&(u.warn=e.warn),e.debug&&(u.debug=e.debug)};var n=o(r(1390)),a=o(r(3901)),s=o(r(9165)),i=r(1883);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function c(e){var t,r;if(e instanceof Error&&!(e instanceof i.UnknownError))return{message:e.message,stack:e.stack,name:e.name};if(null!=(t=e)&&t.error){e.error=c(e.error),e.message=null!=(r=e.message)?r:e.error.message}return e}var u={error:function(e,t){t=c(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=u},5440:e=>{e.exports=function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}},e.exports.__esModule=!0,e.exports.default=e.exports},5591:(e,t,r)=>{var o=r(2402),n=r(7476);e.exports=function e(t,r){var a;this.next||(n(e.prototype),n(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),n(this,"_invoke",function(e,n,s){function i(){return new r(function(n,a){!function e(n,a,s,i){try{var l=t[n](a),c=l.value;return c instanceof o?r.resolve(c.v).then(function(t){e("next",t,s,i)},function(t){e("throw",t,s,i)}):r.resolve(c).then(function(e){l.value=e,s(l)},function(t){return e("throw",t,s,i)})}catch(e){i(e)}}(e,s,n,a)})}return a=a?a.then(i,i):i()},!0)},e.exports.__esModule=!0,e.exports.default=e.exports},5593:(e,t,r)=>{var o=r(4947),n=r(1727),a=r(4514),s=r(7034);e.exports=function(e,t){return o(e)||n(e,t)||a(e,t)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},6045:(e,t,r)=>{var o=r(6346);function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>a});var o=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}function s(...e){return o.useCallback(a(...e),e)}},6268:(e,t,r)=>{var o=r(913),n=r(9828);e.exports=function(e,t,r){if(o())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var s=new(e.bind.apply(e,a));return r&&n(s,r.prototype),s},e.exports.__esModule=!0,e.exports.default=e.exports},6346:(e,t,r)=>{var o=r(7382).default,n=r(8919);e.exports=function(e){var t=n(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},6620:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},7034:e=>{e.exports=function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},7149:(e,t,r)=>{var o=r(8977);e.exports=function(e,t,r,n,a){var s=o(e,t,r,n,a);return s.next().then(function(e){return e.done?e.value:s.next()})},e.exports.__esModule=!0,e.exports.default=e.exports},7382:e=>{function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},7476:e=>{function t(r,o,n,a){var s=Object.defineProperty;try{s({},"",{})}catch(e){s=0}e.exports=t=function(e,r,o,n){if(r)s?s(e,r,{value:o,enumerable:!n,configurable:!n,writable:!n}):e[r]=o;else{var a=function(r,o){t(e,r,function(e){return this._invoke(r,o,e)})};a("next",0),a("throw",1),a("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,o,n,a)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},7824:e=>{function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},8079:e=>{e.exports=function(e){var t=Object(e),r=[];for(var o in t)r.unshift(o);return function e(){for(;r.length;)if((o=r.pop())in t)return e.value=o,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports},8109:e=>{e.exports=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},8683:(e,t,r)=>{var o=r(9828);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},8919:(e,t,r)=>{var o=r(7382).default;e.exports=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},8977:(e,t,r)=>{var o=r(375),n=r(5591);e.exports=function(e,t,r,a,s){return new n(o().w(e,t,r,a),s||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports},9165:e=>{function t(e,t,r,o,n,a,s){try{var i=e[a](s),l=i.value}catch(e){return void r(e)}i.done?t(l):Promise.resolve(l).then(o,n)}e.exports=function(e){return function(){var r=this,o=arguments;return new Promise(function(n,a){var s=e.apply(r,o);function i(e){t(s,n,a,i,l,"next",e)}function l(e){t(s,n,a,i,l,"throw",e)}i(void 0)})}},e.exports.__esModule=!0,e.exports.default=e.exports},9688:(e,t,r)=>{"use strict";r.d(t,{QP:()=>ec});let o=e=>{let t=i(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||s(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},a=/^\[(.+)\]$/,s=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},i=e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)l(r[e],o,e,t);return o},l=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e)return u(e)?void l(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{l(n,c(t,e),r,o)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},u=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,a)=>{r.set(n,a),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t,r=[],o=0,n=0,a=0;for(let s=0;s<e.length;s++){let i=e[s];if(0===o&&0===n){if(":"===i){r.push(e.slice(a,s)),a=s+1;continue}if("/"===i){t=s;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let s=0===r.length?e:e.substring(a),i=p(s);return{modifiers:r,hasImportantModifier:i!==s,baseClassName:i,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}},b=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:m(e),...o(e)}),h=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=t,s=[],i=e.trim().split(h),l="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(c){l=t+(l.length>0?" "+l:l);continue}let m=!!p,b=o(m?f.substring(0,p):f);if(!b){if(!m||!(b=o(f))){l=t+(l.length>0?" "+l:l);continue}m=!1}let h=a(u).join(":"),g=d?h+"!":h,v=g+b;if(s.includes(v))continue;s.push(v);let x=n(b,m);for(let e=0;e<x.length;++e){let t=x[e];s.push(g+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=x(e))&&(o&&(o+=" "),o+=t);return o}let x=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=x(e[o]))&&(r&&(r+=" "),r+=t);return r},y=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,_=/^\d+\/\d+$/,O=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,j=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,P=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>_.test(e),R=e=>!!e&&!Number.isNaN(Number(e)),A=e=>!!e&&Number.isInteger(Number(e)),C=e=>e.endsWith("%")&&R(e.slice(0,-1)),N=e=>O.test(e),z=()=>!0,T=e=>j.test(e)&&!E.test(e),L=()=>!1,U=e=>S.test(e),I=e=>P.test(e),D=e=>!G(e)&&!X(e),$=e=>ee(e,en,L),G=e=>w.test(e),W=e=>ee(e,ea,T),B=e=>ee(e,es,R),F=e=>ee(e,er,L),H=e=>ee(e,eo,I),V=e=>ee(e,el,U),X=e=>k.test(e),q=e=>et(e,ea),J=e=>et(e,ei),Z=e=>et(e,er),K=e=>et(e,en),Q=e=>et(e,eo),Y=e=>et(e,el,!0),ee=(e,t,r)=>{let o=w.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},et=(e,t,r=!1)=>{let o=k.exec(e);return!!o&&(o[1]?t(o[1]):r)},er=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,es=e=>"number"===e,ei=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...t){let r,o,n,a=function(i){return o=(r=b(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,a=s,s(i)};function s(e){let t=o(e);if(t)return t;let a=g(e,r);return n(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=y("color"),t=y("font"),r=y("text"),o=y("font-weight"),n=y("tracking"),a=y("leading"),s=y("breakpoint"),i=y("container"),l=y("spacing"),c=y("radius"),u=y("shadow"),d=y("inset-shadow"),f=y("text-shadow"),p=y("drop-shadow"),m=y("blur"),b=y("perspective"),h=y("aspect"),g=y("ease"),v=y("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),X,G],_=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto","contain","none"],j=()=>[X,G,l],E=()=>[M,"full","auto",...j()],S=()=>[A,"none","subgrid",X,G],P=()=>["auto",{span:["full",A,X,G]},A,X,G],T=()=>[A,"auto",X,G],L=()=>["auto","min","max","fr",X,G],U=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...j()],et=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...j()],er=()=>[e,X,G],eo=()=>[...w(),Z,F,{position:[X,G]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",K,$,{size:[X,G]}],es=()=>[C,q,W],ei=()=>["","none","full",c,X,G],el=()=>["",R,q,W],ec=()=>["solid","dashed","dotted","double"],eu=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[R,C,Z,F],ef=()=>["","none",m,X,G],ep=()=>["none",R,X,G],em=()=>["none",R,X,G],eb=()=>[R,X,G],eh=()=>[M,"full",...j()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[z],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:["px",R],text:[N],"text-shadow":[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,G,X,h]}],container:["container"],columns:[{columns:[R,G,X,i]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:E()}],"inset-x":[{"inset-x":E()}],"inset-y":[{"inset-y":E()}],start:[{start:E()}],end:[{end:E()}],top:[{top:E()}],right:[{right:E()}],bottom:[{bottom:E()}],left:[{left:E()}],visibility:["visible","invisible","collapse"],z:[{z:[A,"auto",X,G]}],basis:[{basis:[M,"full","auto",i,...j()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[R,M,"auto","initial","none",G]}],grow:[{grow:["",R,X,G]}],shrink:[{shrink:["",R,X,G]}],order:[{order:[A,"first","last","none",X,G]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:P()}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:P()}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":L()}],"auto-rows":[{"auto-rows":L()}],gap:[{gap:j()}],"gap-x":[{"gap-x":j()}],"gap-y":[{"gap-y":j()}],"justify-content":[{justify:[...U(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...U()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":U()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:j()}],px:[{px:j()}],py:[{py:j()}],ps:[{ps:j()}],pe:[{pe:j()}],pt:[{pt:j()}],pr:[{pr:j()}],pb:[{pb:j()}],pl:[{pl:j()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":j()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":j()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[i,"screen",...et()]}],"min-w":[{"min-w":[i,"screen","none",...et()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,q,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,X,B]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",C,G]}],"font-family":[{font:[J,G,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,X,G]}],"line-clamp":[{"line-clamp":[R,"none",X,B]}],leading:[{leading:[a,...j()]}],"list-image":[{"list-image":["none",X,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[R,"from-font","auto",X,W]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[R,"auto",X,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:j()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},A,X,G],radial:["",X,G],conic:[A,X,G]},Q,H]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[R,X,G]}],"outline-w":[{outline:["",R,q,W]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,Y,V]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,Y,V]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[R,W]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Y,V]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[R,X,G]}],"mix-blend":[{"mix-blend":[...eu(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eu()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[R]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[X,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[R]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,G]}],filter:[{filter:["","none",X,G]}],blur:[{blur:ef()}],brightness:[{brightness:[R,X,G]}],contrast:[{contrast:[R,X,G]}],"drop-shadow":[{"drop-shadow":["","none",p,Y,V]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",R,X,G]}],"hue-rotate":[{"hue-rotate":[R,X,G]}],invert:[{invert:["",R,X,G]}],saturate:[{saturate:[R,X,G]}],sepia:[{sepia:["",R,X,G]}],"backdrop-filter":[{"backdrop-filter":["","none",X,G]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[R,X,G]}],"backdrop-contrast":[{"backdrop-contrast":[R,X,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",R,X,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[R,X,G]}],"backdrop-invert":[{"backdrop-invert":["",R,X,G]}],"backdrop-opacity":[{"backdrop-opacity":[R,X,G]}],"backdrop-saturate":[{"backdrop-saturate":[R,X,G]}],"backdrop-sepia":[{"backdrop-sepia":["",R,X,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":j()}],"border-spacing-x":[{"border-spacing-x":j()}],"border-spacing-y":[{"border-spacing-y":j()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[R,"initial",X,G]}],ease:[{ease:["linear","initial",g,X,G]}],delay:[{delay:[R,X,G]}],animate:[{animate:["none",v,X,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,X,G]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[X,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":j()}],"scroll-mx":[{"scroll-mx":j()}],"scroll-my":[{"scroll-my":j()}],"scroll-ms":[{"scroll-ms":j()}],"scroll-me":[{"scroll-me":j()}],"scroll-mt":[{"scroll-mt":j()}],"scroll-mr":[{"scroll-mr":j()}],"scroll-mb":[{"scroll-mb":j()}],"scroll-ml":[{"scroll-ml":j()}],"scroll-p":[{"scroll-p":j()}],"scroll-px":[{"scroll-px":j()}],"scroll-py":[{"scroll-py":j()}],"scroll-ps":[{"scroll-ps":j()}],"scroll-pe":[{"scroll-pe":j()}],"scroll-pt":[{"scroll-pt":j()}],"scroll-pr":[{"scroll-pr":j()}],"scroll-pb":[{"scroll-pb":j()}],"scroll-pl":[{"scroll-pl":j()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,G]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[R,q,W,B]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,TL:()=>s});var o=r(2115),n=r(6101),a=r(5155);function s(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...a}=e;if(o.isValidElement(r)){var s;let e,i,l=(s=r,(i=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(i=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),c=function(e,t){let r={...t};for(let o in t){let n=e[o],a=t[o];/^on[A-Z]/.test(o)?n&&a?r[o]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[o]=n):"style"===o?r[o]={...n,...a}:"className"===o&&(r[o]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==o.Fragment&&(c.ref=t?(0,n.t)(t,l):l),o.cloneElement(r,c)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...s}=e,i=o.Children.toArray(n),l=i.find(c);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...s,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var i=s("Slot"),l=Symbol("radix.slottable");function c(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9828:e=>{function t(r,o){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,o)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var o=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:u="",children:d,iconNode:f,...p}=e;return(0,o.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:s?24*Number(a)/Number(n):a,className:i("lucide",u),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,o.forwardRef)((r,a)=>{let{className:l,...c}=r;return(0,o.createElement)(u,{ref:a,iconNode:t,className:i("lucide-".concat(n(s(e))),"lucide-".concat(e),l),...c})});return r.displayName=s(e),r}}}]);