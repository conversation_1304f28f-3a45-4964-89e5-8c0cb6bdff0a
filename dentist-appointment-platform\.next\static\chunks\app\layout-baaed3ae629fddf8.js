(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1337:(e,s,t)=>{"use strict";t.d(s,{default:()=>er});var r=t(5155),a=t(2115),n=t(5695),i=t(2911),l=t(8366),o=t(3294),d=t(9385),c=t(6874),m=t.n(c),u=t(285),h=t(6126),x=t(7340),f=t(9074),p=t(5273),g=t(7580),N=t(1007),v=t(7434),j=t(1497),b=t(2713),w=t(3917),A=t(5525),y=t(381),D=t(4788),T=t(3052),k=t(2355);let S=[{name:"Dashboard",href:"/dashboard",icon:x.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN]},{name:"Appointments",href:"/appointments",icon:f.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN],children:[{name:"My Appointments",href:"/appointments",icon:f.A,roles:[d.g.PATIENT]},{name:"Book Appointment",href:"/appointments/book",icon:f.A,roles:[d.g.PATIENT]},{name:"All Appointments",href:"/appointments",icon:f.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Schedule",href:"/appointments/schedule",icon:p.A,roles:[d.g.DENTIST,d.g.ADMIN]}]},{name:"Patients",href:"/patients",icon:g.A,roles:[d.g.DENTIST,d.g.ADMIN],children:[{name:"All Patients",href:"/patients",icon:g.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Add Patient",href:"/patients/add",icon:N.A,roles:[d.g.DENTIST,d.g.ADMIN]}]},{name:"Documents",href:"/documents",icon:v.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN],children:[{name:"My Documents",href:"/documents",icon:v.A,roles:[d.g.PATIENT]},{name:"Patient Documents",href:"/documents",icon:v.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Upload Document",href:"/documents/upload",icon:v.A,roles:[d.g.DENTIST,d.g.ADMIN]}]},{name:"Profile",href:"/profile",icon:N.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN]},{name:"Messages",href:"/messages",icon:j.A,badge:3,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN]},{name:"Reports",href:"/reports",icon:b.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Practice Management",href:"/practice",icon:w.A,roles:[d.g.DENTIST,d.g.ADMIN],children:[{name:"Working Hours",href:"/practice/hours",icon:f.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Services",href:"/practice/services",icon:p.A,roles:[d.g.DENTIST,d.g.ADMIN]},{name:"Staff",href:"/practice/staff",icon:g.A,roles:[d.g.ADMIN]}]},{name:"Administration",href:"/admin",icon:A.A,roles:[d.g.ADMIN],children:[{name:"User Management",href:"/admin/users",icon:g.A,roles:[d.g.ADMIN]},{name:"System Settings",href:"/admin/settings",icon:y.A,roles:[d.g.ADMIN]},{name:"Audit Logs",href:"/admin/audit",icon:v.A,roles:[d.g.ADMIN]}]}],I=[{name:"Settings",href:"/settings",icon:y.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN]},{name:"Help & Support",href:"/help",icon:D.A,roles:[d.g.PATIENT,d.g.DENTIST,d.g.ADMIN]}],E=()=>{let e=(0,n.usePathname)(),{sidebarCollapsed:s,sidebarOpen:t,setSidebarOpen:a,toggleSidebar:d}=(0,l.cL)(),{user:c}=(0,o.As)(),x=e=>c?e.filter(e=>!!(!e.roles||e.roles.includes(c.role))&&(e.children&&(e.children=x(e.children)),!0)):[],f=x(S),p=x(I),g=s=>"/dashboard"===s?"/dashboard"===e||"/"===e:null==e?void 0:e.startsWith(s),N=e=>{let{item:t,level:n=0}=e,l=g(t.href),o=t.children&&t.children.length>0,d=o&&t.children.some(e=>g(e.href));return(0,r.jsxs)("div",{children:[(0,r.jsxs)(m(),{href:t.href,className:(0,i.cn)("group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground",n>0&&"ml-4 pl-6",l||d?"bg-accent text-accent-foreground":"text-muted-foreground",s&&0===n&&"justify-center px-2"),onClick:()=>{window.innerWidth<768&&a(!1)},children:[(0,r.jsx)(t.icon,{className:(0,i.cn)("h-5 w-5 shrink-0",s&&0===n?"mr-0":"mr-3")}),(!s||n>0)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"truncate",children:t.name}),t.badge&&(0,r.jsx)(h.E,{variant:"secondary",className:"ml-auto h-5 w-5 rounded-full p-0 text-xs",children:t.badge})]})]}),o&&(!s||n>0)&&(0,r.jsx)("div",{className:"mt-1 space-y-1",children:t.children.map(e=>(0,r.jsx)(N,{item:e,level:n+1},e.href))})]})};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:(0,i.cn)("fixed left-0 top-0 z-50 h-full bg-background border-r border-border transition-all duration-300 ease-in-out hidden md:flex flex-col",s?"w-20":"w-72"),children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-border",children:[!s&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,r.jsx)("span",{className:"text-lg font-semibold",children:"DentistApp"})]}),(0,r.jsx)(u.$,{variant:"ghost",size:"icon",onClick:d,className:"h-8 w-8",children:s?(0,r.jsx)(T.A,{className:"h-4 w-4"}):(0,r.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 p-4 overflow-y-auto",children:f.map(e=>(0,r.jsx)(N,{item:e},e.href))}),(0,r.jsx)("div",{className:"border-t border-border p-4 space-y-2",children:p.map(e=>(0,r.jsx)(N,{item:e},e.href))})]}),(0,r.jsxs)("div",{className:(0,i.cn)("fixed left-0 top-0 z-50 h-full w-72 bg-background border-r border-border transition-transform duration-300 ease-in-out md:hidden flex flex-col",t?"translate-x-0":"-translate-x-full"),children:[(0,r.jsx)("div",{className:"flex h-16 items-center px-4 border-b border-border",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,r.jsx)("span",{className:"text-lg font-semibold",children:"DentistApp"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 p-4 overflow-y-auto",children:f.map(e=>(0,r.jsx)(N,{item:e},e.href))}),(0,r.jsx)("div",{className:"border-t border-border p-4 space-y-2",children:p.map(e=>(0,r.jsx)(N,{item:e},e.href))})]})]})};var C=t(8279);function M(e){let{...s}=e;return(0,r.jsx)(C.bL,{"data-slot":"dropdown-menu",...s})}function P(e){let{...s}=e;return(0,r.jsx)(C.l9,{"data-slot":"dropdown-menu-trigger",...s})}function z(e){let{className:s,sideOffset:t=4,...a}=e;return(0,r.jsx)(C.ZL,{children:(0,r.jsx)(C.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",s),...a})})}function R(e){let{className:s,inset:t,variant:a="default",...n}=e;return(0,r.jsx)(C.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n})}function L(e){let{className:s,inset:t,...a}=e;return(0,r.jsx)(C.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",s),...a})}function O(e){let{className:s,...t}=e;return(0,r.jsx)(C.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",s),...t})}var B=t(4011);function $(e){let{className:s,...t}=e;return(0,r.jsx)(B.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",s),...t})}function _(e){let{className:s,...t}=e;return(0,r.jsx)(B._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",s),...t})}function W(e){let{className:s,...t}=e;return(0,r.jsx)(B.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",s),...t})}var H=t(4783),U=t(7924),G=t(3861),Z=t(2098),F=t(3509),J=t(4738),V=t(4835);let q=()=>{let e=(0,n.useRouter)(),{toggleSidebar:s,setSidebarOpen:t}=(0,l.cL)(),{pageTitle:a}=(0,l.Sw)(),{notifications:d,clearNotifications:c}=(0,l.E$)(),{user:m,isAuthenticated:x}=(0,o.As)(),{logout:f}=(0,o.BG)();if(!x||!m)return null;let p=d.filter(e=>!e.action),g=async()=>{await f(),e.push("/")},v=()=>{e.push("/notifications")};return(0,r.jsx)("header",{className:"sticky top-0 z-30 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>t(!0),children:[(0,r.jsx)(H.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]}),(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"hidden md:flex",onClick:s,children:[(0,r.jsx)(H.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:a})})]}),(0,r.jsx)("div",{className:"hidden lg:flex flex-1 max-w-md mx-8",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(U.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)("input",{type:"search",placeholder:"Search...",className:"w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"lg:hidden",children:[(0,r.jsx)(U.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Search"})]}),(0,r.jsxs)(M,{children:[(0,r.jsx)(P,{asChild:!0,children:(0,r.jsxs)(u.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,r.jsx)(G.A,{className:"h-5 w-5"}),p.length>0&&(0,r.jsx)(h.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs",children:p.length>9?"9+":p.length}),(0,r.jsx)("span",{className:"sr-only",children:"Notifications"})]})}),(0,r.jsxs)(z,{align:"end",className:"w-80",children:[(0,r.jsxs)(L,{className:"flex items-center justify-between",children:["Notifications",d.length>0&&(0,r.jsx)(u.$,{variant:"ghost",size:"sm",onClick:c,className:"h-auto p-0 text-xs text-muted-foreground hover:text-foreground",children:"Clear all"})]}),(0,r.jsx)(O,{}),0===d.length?(0,r.jsx)("div",{className:"p-4 text-center text-sm text-muted-foreground",children:"No notifications"}):(0,r.jsxs)("div",{className:"max-h-64 overflow-y-auto",children:[d.slice(0,5).map(e=>(0,r.jsx)(R,{className:"flex flex-col items-start p-3 cursor-pointer",onClick:v,children:(0,r.jsxs)("div",{className:"flex w-full items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.message&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:i.DR.truncate(e.message,60)})]}),(0,r.jsx)(h.E,{variant:"error"===e.type?"destructive":"warning"===e.type?"secondary":"default",className:"ml-2 text-xs",children:e.type})]})},e.id)),d.length>5&&(0,r.jsx)(R,{className:"text-center text-sm text-primary",onClick:v,children:"View all notifications"})]})]})]}),(0,r.jsxs)(M,{children:[(0,r.jsx)(P,{asChild:!0,children:(0,r.jsx)(u.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full",children:(0,r.jsxs)($,{className:"h-10 w-10",children:[(0,r.jsx)(_,{src:null==m?void 0:m.image,alt:(null==m?void 0:m.name)||"User"}),(0,r.jsx)(W,{className:"bg-primary/10 text-primary",children:(null==m?void 0:m.name)?i.DR.initials(m.name):"U"})]})})}),(0,r.jsxs)(z,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsx)(L,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:(null==m?void 0:m.name)||"User"}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null==m?void 0:m.email}),(0,r.jsx)(h.E,{variant:"outline",className:"w-fit text-xs mt-1",children:i.DR.capitalizeWords((null==m?void 0:m.role.toLowerCase())||"")})]})}),(0,r.jsx)(O,{}),(0,r.jsxs)(R,{onClick:()=>{e.push("/profile")},children:[(0,r.jsx)(N.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]}),(0,r.jsxs)(R,{onClick:()=>{e.push("/settings")},children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsxs)(R,{onClick:()=>e.push("/help"),children:[(0,r.jsx)(D.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Help & Support"})]}),(0,r.jsx)(O,{}),(0,r.jsx)(L,{className:"text-xs text-muted-foreground",children:"Theme"}),(0,r.jsxs)(R,{children:[(0,r.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Light"})]}),(0,r.jsxs)(R,{children:[(0,r.jsx)(F.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dark"})]}),(0,r.jsxs)(R,{children:[(0,r.jsx)(J.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"System"})]}),(0,r.jsx)(O,{}),(0,r.jsxs)(R,{onClick:g,className:"text-destructive",children:[(0,r.jsx)(V.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]})]})]})})},K=e=>{var s;let{items:t,className:a,separator:l=(0,r.jsx)(T.A,{className:"h-4 w-4 text-muted-foreground"}),showHome:o=!0,maxItems:d=5}=e,c=(0,n.useRouter)(),u=o&&(null==(s=t[0])?void 0:s.label)!=="Home"?[{label:"Home",href:"/dashboard",icon:x.A},...t]:t,h=u.length>d?[u[0],{label:"...",href:void 0},...u.slice(-d+2)]:u,f=(e,s)=>{e.href&&!e.current&&c.push(e.href)};return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center space-x-1 text-sm",a),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:h.map((e,s)=>{let t=s===h.length-1,a=e.current||t,n="..."===e.label;return(0,r.jsxs)("li",{className:"flex items-center",children:[s>0&&(0,r.jsx)("span",{className:"mx-2 flex-shrink-0",children:l}),n?(0,r.jsx)("span",{className:"text-muted-foreground px-2",children:"..."}):(0,r.jsx)("div",{className:"flex items-center",children:e.href&&!a?(0,r.jsxs)(m(),{href:e.href,className:(0,i.cn)("flex items-center space-x-1 rounded-md px-2 py-1 transition-colors hover:bg-accent hover:text-accent-foreground","text-muted-foreground hover:text-foreground"),onClick:t=>{t.preventDefault(),f(e,s)},children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate max-w-[150px] sm:max-w-[200px]",children:e.label})]}):(0,r.jsxs)("span",{className:(0,i.cn)("flex items-center space-x-1 px-2 py-1 rounded-md",a?"text-foreground font-medium bg-accent/50":"text-muted-foreground"),"aria-current":a?"page":void 0,children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate max-w-[150px] sm:max-w-[200px]",children:e.label})]})})]},"".concat(e.label,"-").concat(s))})})})};var X=t(6695),Q=t(1243),Y=t(3904),ee=t(446);class es extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:"error_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s),this.setState({error:e,errorInfo:s}),this.props.onError&&this.props.onError(e,s),this.logErrorToService(e,s)}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let{error:e,errorInfo:s,errorId:t}=this.state,{level:a="component",showDetails:n=!1}=this.props;return"critical"===a?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,r.jsxs)(X.Zp,{className:"w-full max-w-2xl glass-card",children:[(0,r.jsxs)(X.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10",children:(0,r.jsx)(Q.A,{className:"h-8 w-8 text-destructive"})}),(0,r.jsx)(X.ZB,{className:"text-2xl",children:"Critical Error"}),(0,r.jsx)(X.BT,{children:"A critical error has occurred that prevents the application from functioning properly."})]}),(0,r.jsxs)(X.Wu,{className:"space-y-4",children:[n&&e&&(0,r.jsxs)("div",{className:"rounded-md bg-muted p-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Error Details:"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:e.message}),t&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Error ID: ",t]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsxs)(u.$,{onClick:this.handleReload,className:"flex-1",children:[(0,r.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),"Reload Page"]}),(0,r.jsxs)(u.$,{variant:"outline",onClick:this.handleGoHome,className:"flex-1",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Go Home"]}),n&&(0,r.jsxs)(u.$,{variant:"outline",onClick:this.copyErrorDetails,children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),"Copy Details"]})]})]})]})}):"page"===a?(0,r.jsx)("div",{className:"flex min-h-[400px] items-center justify-center p-4",children:(0,r.jsxs)(X.Zp,{className:"w-full max-w-lg glass-card",children:[(0,r.jsxs)(X.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10",children:(0,r.jsx)(Q.A,{className:"h-6 w-6 text-destructive"})}),(0,r.jsx)(X.ZB,{children:"Something went wrong"}),(0,r.jsx)(X.BT,{children:"We encountered an error while loading this page."})]}),(0,r.jsxs)(X.Wu,{className:"space-y-4",children:[n&&e&&(0,r.jsxs)("div",{className:"rounded-md bg-muted p-3",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:e.message}),t&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Error ID: ",t]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(u.$,{onClick:this.handleRetry,className:"flex-1",children:[(0,r.jsx)(Y.A,{className:"mr-2 h-4 w-4"}),"Try Again"]}),(0,r.jsxs)(u.$,{variant:"outline",onClick:this.handleGoHome,children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Home"]})]})]})]})}):(0,r.jsx)("div",{className:"rounded-md border border-destructive/20 bg-destructive/5 p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(Q.A,{className:"h-5 w-5 text-destructive mt-0.5"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-destructive",children:"Component Error"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This component failed to render properly."}),n&&e&&(0,r.jsxs)("details",{className:"text-xs",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-muted-foreground hover:text-foreground",children:"Show details"}),(0,r.jsx)("pre",{className:"mt-2 whitespace-pre-wrap font-mono text-xs bg-muted p-2 rounded",children:e.message})]}),(0,r.jsxs)(u.$,{size:"sm",variant:"outline",onClick:this.handleRetry,children:[(0,r.jsx)(Y.A,{className:"mr-1 h-3 w-3"}),"Retry"]})]})]})})}return this.props.children}constructor(e){super(e),this.logErrorToService=(e,s)=>{console.log("Error logged:",{message:e.message,stack:e.stack,componentStack:s.componentStack,errorId:this.state.errorId,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href})},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null,errorId:null})},this.handleReload=()=>{window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.copyErrorDetails=()=>{let{error:e,errorInfo:s,errorId:t}=this.state,r={errorId:t,message:null==e?void 0:e.message,stack:null==e?void 0:e.stack,componentStack:null==s?void 0:s.componentStack,timestamp:new Date().toISOString(),url:window.location.href};navigator.clipboard.writeText(JSON.stringify(r,null,2))},this.state={hasError:!1,error:null,errorInfo:null,errorId:null}}}var et=t(3045);let er=e=>{let{children:s}=e,t=(0,n.usePathname)(),{sidebarCollapsed:d,sidebarOpen:c,setSidebarOpen:m}=(0,l.cL)(),{pageTitle:u,breadcrumbs:h}=(0,l.Sw)(),{user:x,isAuthenticated:f}=(0,o.As)(),[p,g]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{let e=()=>{g(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{p&&m(!1)},[t,p,m]),(null==t?void 0:t.startsWith("/auth"))||"/login"===t||"/register"===t||!f)?(0,r.jsx)(es,{level:"critical",children:(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[s,(0,r.jsx)(et.Toaster,{})]})}):(0,r.jsx)(es,{level:"critical",children:(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsx)(E,{}),p&&c&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden",onClick:()=>m(!1)}),(0,r.jsxs)("div",{className:(0,i.cn)("flex flex-col min-h-screen transition-all duration-300 ease-in-out",p?"ml-0":d?"ml-20":"ml-72"),children:[(0,r.jsx)(q,{}),(0,r.jsxs)("main",{className:"flex-1 flex flex-col",children:[h.length>0&&(0,r.jsx)("div",{className:"border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-3",children:(0,r.jsx)(K,{items:h})})}),(0,r.jsx)("div",{className:"flex-1 container mx-auto px-4 py-6",children:(0,r.jsx)(es,{level:"page",children:s})})]})]}),(0,r.jsx)(et.Toaster,{})]})})}},2876:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(5155),a=t(2108),n=t(2115),i=t(3294);let l=()=>{let{data:e,status:s}=(0,a.useSession)(),{setUser:t,setSession:r,setLoading:l}=(0,i.nc)();return(0,n.useEffect)(()=>{if(l("loading"===s),"authenticated"===s&&(null==e?void 0:e.user)){let s={id:e.user.id,email:e.user.email,name:e.user.name,role:e.user.role,image:e.user.image,createdAt:new Date,updatedAt:new Date},a={user:s,accessToken:e.accessToken,expiresAt:new Date(Date.now()+864e5)};t(s),r(a)}else"unauthenticated"===s&&(t(null),r(null))},[e,s,t,r,l]),null};function o(e){let{children:s}=e;return(0,r.jsxs)(a.SessionProvider,{children:[(0,r.jsx)(l,{}),s]})}},3045:(e,s,t)=>{"use strict";t.d(s,{Toaster:()=>i});var r=t(5155),a=t(1362),n=t(6671);let i=e=>{let{...s}=e,{theme:t="system"}=(0,a.D)();return(0,r.jsx)(n.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...s})}},4274:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6707,23)),Promise.resolve().then(t.t.bind(t,2305,23)),Promise.resolve().then(t.t.bind(t,786,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,1337)),Promise.resolve().then(t.bind(t,9914)),Promise.resolve().then(t.bind(t,2876)),Promise.resolve().then(t.bind(t,3045))},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>o});var r=t(5155);t(2115);var a=t(9708),n=t(2085),i=t(2911);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:t,asChild:n=!1,...o}=e,d=n?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),s),...o})}},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i});var r=t(5155);t(2115);var a=t(2911);function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}},8366:(e,s,t)=>{"use strict";t.d(s,{E$:()=>d,Sw:()=>c,cL:()=>o});var r=t(5453),a=t(6786);t(9385);var n=function(e){return e.SUCCESS="success",e.ERROR="error",e.WARNING="warning",e.INFO="info",e}({});let i=()=>"notification-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),l=(0,r.v)()((0,a.Zr)((e,s)=>({theme:"dark",sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[],setTheme:s=>{e({theme:s});{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===s){let s=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(s)}else e.classList.add(s)}},toggleSidebar:()=>{e(e=>({sidebarCollapsed:!e.sidebarCollapsed}))},setSidebarOpen:s=>{e({sidebarOpen:s})},setSidebarCollapsed:s=>{e({sidebarCollapsed:s})},setGlobalLoading:function(s){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";e({globalLoading:s,loadingMessage:t})},addNotification:t=>{let r={...t,id:i(),createdAt:new Date};e(e=>({notifications:[...e.notifications,r]})),r.duration&&r.duration>0&&setTimeout(()=>{s().removeNotification(r.id)},r.duration)},removeNotification:s=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==s)}))},clearNotifications:()=>{e({notifications:[]})},openModal:s=>{e(e=>({modals:{...e.modals,[s]:!0}}))},closeModal:s=>{e(e=>({modals:{...e.modals,[s]:!1}}))},toggleModal:s=>{e(e=>({modals:{...e.modals,[s]:!e.modals[s]}}))},isModalOpen:e=>s().modals[e]||!1,setPageTitle:s=>{e({pageTitle:s}),document.title="".concat(s," | Dentist Appointment Platform")},setBreadcrumbs:s=>{e({breadcrumbs:s})},addBreadcrumb:s=>{e(e=>({breadcrumbs:[...e.breadcrumbs,s]}))},reset:()=>{e({sidebarCollapsed:!1,sidebarOpen:!1,globalLoading:!1,loadingMessage:"",notifications:[],modals:{},pageTitle:"Dentist Appointment Platform",breadcrumbs:[]})}}),{name:"ui-storage",storage:(0,a.KU)(()=>localStorage),partialize:e=>({theme:e.theme,sidebarCollapsed:e.sidebarCollapsed})})),o=()=>{let e=l(e=>e.sidebarCollapsed),s=l(e=>e.sidebarOpen),t=l(e=>e.toggleSidebar);return{sidebarCollapsed:e,sidebarOpen:s,toggleSidebar:t,setSidebarOpen:l(e=>e.setSidebarOpen),setSidebarCollapsed:l(e=>e.setSidebarCollapsed)}},d=()=>{let e=l(e=>e.notifications),s=l(e=>e.addNotification),t=l(e=>e.removeNotification);return{notifications:e,addNotification:s,removeNotification:t,clearNotifications:l(e=>e.clearNotifications),showSuccess:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;s({type:n.SUCCESS,title:e,message:t,duration:r})},showError:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;s({type:n.ERROR,title:e,message:t,duration:r})},showWarning:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:7e3;s({type:n.WARNING,title:e,message:t,duration:r})},showInfo:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;s({type:n.INFO,title:e,message:t,duration:r})}}},c=()=>{let e=l(e=>e.pageTitle),s=l(e=>e.breadcrumbs),t=l(e=>e.setPageTitle);return{pageTitle:e,breadcrumbs:s,setPageTitle:t,setBreadcrumbs:l(e=>e.setBreadcrumbs),addBreadcrumb:l(e=>e.addBreadcrumb)}}},9914:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(5155),a=t(2115),n=t(3294),i=t(9385);let l=e=>{let{children:s}=e,{setUser:t,setSession:l}=(0,n.nc)();return(0,a.useEffect)(()=>{let e={id:"mock-user-1",email:"<EMAIL>",name:"Dr. John Smith",image:null,role:i.g.DENTIST,createdAt:new Date,updatedAt:new Date,dentistProfile:{id:"dentist-profile-1",userId:"mock-user-1",licenseNumber:"DDS-12345",specialization:["General Dentistry","Cosmetic Dentistry"],workingHours:{monday:[{start:"09:00",end:"17:00"}],tuesday:[{start:"09:00",end:"17:00"}],wednesday:[{start:"09:00",end:"17:00"}],thursday:[{start:"09:00",end:"17:00"}],friday:[{start:"09:00",end:"17:00"}],saturday:[],sunday:[]},practiceInfo:{name:"Smith Dental Practice",address:"123 Main St, City, State 12345",phone:"(*************",email:"<EMAIL>",website:"https://smithdental.com"},createdAt:new Date,updatedAt:new Date,user:{}}},s={user:e,accessToken:"mock-access-token",expiresAt:new Date(Date.now()+864e5)};t(e),l(s)},[t,l]),(0,r.jsx)(r.Fragment,{children:s})}}},e=>{var s=s=>e(e.s=s);e.O(0,[486,52,919,3,764,698,441,684,358],()=>s(4274)),_N_E=e.O()}]);