(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8462:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(60687),a=t(43210),n=t(65106),i=t(16189),l=t(99720),d=t(53881);let o=(e={})=>{let s=(0,i.useRouter)(),{user:t,isAuthenticated:r,isLoading:n}=(0,l.As)(),{checkPermission:d,hasRole:o}=(0,l.BG)(),{requiredRole:p,requiredPermissions:m=[],redirectTo:x="/auth/login",onUnauthorized:u}=e;return(0,a.useEffect)(()=>{if(!n){if(!r||!t)return void(u?u():s.push(x));if(p&&!o(p)){if(u)u();else{let e=c(t.role);s.push(e)}return}if(m.length>0&&!m.every(e=>d(e)))return void(u?u():s.push("/unauthorized"))}},[r,t,n,p,m,x,u,s,d,o]),{isAuthenticated:r,user:t,isLoading:n,isAuthorized:r&&(!p||o(p))&&(0===m.length||m.every(e=>d(e)))}},c=e=>{switch(e){case d.g.PATIENT:return"/patient/dashboard";case d.g.DENTIST:return"/dentist/dashboard";case d.g.ADMIN:return"/admin/dashboard";default:return"/dashboard"}};var p=t(44493),m=t(29523),x=t(96834),u=t(40228),h=t(41312),g=t(10022),f=t(48730),j=t(62688);let b=(0,j.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),v=(0,j.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var N=t(93613),y=t(5336);let w=()=>{let{setPageTitle:e,setBreadcrumbs:s}=(0,n.Sw)(),{isAuthorized:t}=o();if((0,a.useEffect)(()=>{e("Dashboard"),s([{label:"Dashboard",current:!0}])},[e,s]),!t)return null;let i=[{title:"Today's Appointments",value:"8",description:"2 pending confirmations",icon:u.A,trend:"+12%",color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Total Patients",value:"1,234",description:"23 new this month",icon:h.A,trend:"+5%",color:"text-green-600",bgColor:"bg-green-50"},{title:"Pending Documents",value:"12",description:"3 require urgent review",icon:g.A,trend:"-8%",color:"text-orange-600",bgColor:"bg-orange-50"},{title:"Average Wait Time",value:"15 min",description:"Down from last week",icon:f.A,trend:"-3 min",color:"text-purple-600",bgColor:"bg-purple-50"}],l=e=>{switch(e){case"confirmed":return(0,r.jsx)(x.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Confirmed"});case"pending":return(0,r.jsx)(x.E,{variant:"secondary",className:"bg-yellow-100 text-yellow-800",children:"Pending"});default:return(0,r.jsx)(x.E,{variant:"outline",children:e})}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Welcome back!"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-1",children:"Here's what's happening at your practice today."})]}),(0,r.jsxs)(m.$,{className:"glass-button",children:[(0,r.jsx)(b,{className:"mr-2 h-4 w-4"}),"New Appointment"]})]}),(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-4",children:i.map((e,s)=>(0,r.jsxs)(p.Zp,{className:"glass-card",children:[(0,r.jsxs)(p.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-muted-foreground",children:e.title}),(0,r.jsx)("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:(0,r.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})})]}),(0,r.jsxs)(p.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-foreground",children:e.value}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-muted-foreground",children:[(0,r.jsx)("span",{children:e.description}),(0,r.jsx)(x.E,{variant:"outline",className:"text-xs",children:e.trend})]})]})]},s))}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(p.Zp,{className:"glass-card lg:col-span-2",children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsxs)(p.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Today's Appointments"})]}),(0,r.jsx)(p.BT,{children:"Manage your appointments for today"})]}),(0,r.jsxs)(p.Wu,{children:[(0,r.jsx)("div",{className:"space-y-4",children:[{id:"1",patient:"John Doe",time:"09:00 AM",type:"Cleaning",status:"confirmed"},{id:"2",patient:"Jane Smith",time:"10:30 AM",type:"Consultation",status:"pending"},{id:"3",patient:"Mike Johnson",time:"02:00 PM",type:"Root Canal",status:"confirmed"},{id:"4",patient:"Sarah Wilson",time:"03:30 PM",type:"Filling",status:"confirmed"}].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border border-border/40 hover:bg-accent/50 transition-colors",children:[(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"font-medium text-foreground",children:e.patient}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:e.type})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-foreground",children:e.time}),l(e.status)]})]},e.id))}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-border/40",children:(0,r.jsx)(m.$,{variant:"outline",className:"w-full",children:"View All Appointments"})})]})]}),(0,r.jsxs)(p.Zp,{className:"glass-card",children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsx)(p.ZB,{children:"Quick Actions"}),(0,r.jsx)(p.BT,{children:"Common tasks and shortcuts"})]}),(0,r.jsxs)(p.Wu,{className:"space-y-3",children:[(0,r.jsxs)(m.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(b,{className:"mr-2 h-4 w-4"}),"Add New Patient"]}),(0,r.jsxs)(m.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Schedule Appointment"]}),(0,r.jsxs)(m.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Upload Document"]}),(0,r.jsxs)(m.$,{variant:"outline",className:"w-full justify-start",children:[(0,r.jsx)(v,{className:"mr-2 h-4 w-4"}),"View Reports"]})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(p.Zp,{className:"glass-card",children:[(0,r.jsx)(p.aR,{children:(0,r.jsxs)(p.ZB,{className:"flex items-center space-x-2 text-orange-600",children:[(0,r.jsx)(N.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Pending Actions"})]})}),(0,r.jsxs)(p.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-orange-50 border border-orange-200",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-orange-600"}),(0,r.jsx)("span",{className:"text-sm text-orange-800",children:"3 appointment confirmations needed"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-red-50 border border-red-200",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)("span",{className:"text-sm text-red-800",children:"2 overdue patient follow-ups"})]})]})]}),(0,r.jsxs)(p.Zp,{className:"glass-card",children:[(0,r.jsx)(p.aR,{children:(0,r.jsxs)(p.ZB,{className:"flex items-center space-x-2 text-green-600",children:[(0,r.jsx)(y.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Recent Completions"})]})}),(0,r.jsxs)(p.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-green-50 border border-green-200",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm text-green-800",children:"5 appointments completed today"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg bg-blue-50 border border-blue-200",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm text-blue-800",children:"12 documents processed this week"})]})]})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26794:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\test\\\\dentist-appointment-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\dashboard\\page.tsx","default")},89842:(e,s,t)=>{Promise.resolve().then(t.bind(t,8462))},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},98281:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\test\\dentist-appointment-platform\\src\\app\\dashboard\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[243,97,658,513],()=>t(98281));module.exports=r})();